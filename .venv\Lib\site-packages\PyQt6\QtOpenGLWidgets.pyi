# The PEP 484 type hints stub file for the QtOpenGLWidgets module.
#
# Generated by SIP 6.12.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing, enum

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore
from PyQt6 import QtGui
from PyQt6 import QtOpenGL
from PyQt6 import QtWidgets

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]


class QOpenGLWidget(QtWidgets.QWidget):

    class TargetBuffer(enum.Enum):
        LeftBuffer = ... # type: QOpenGLWidget.TargetBuffer
        RightBuffer = ... # type: QOpenGLWidget.TargetBuffer

    class UpdateBehavior(enum.Enum):
        NoPartialUpdate = ... # type: QOpenGLWidget.UpdateBehavior
        PartialUpdate = ... # type: QOpenGLWidget.UpdateBehavior

    def __init__(self, parent: typing.Optional[QtWidgets.QWidget] = ..., flags: QtCore.Qt.WindowType = ...) -> None: ...

    def currentTargetBuffer(self) -> 'QOpenGLWidget.TargetBuffer': ...
    def setTextureFormat(self, texFormat: int) -> None: ...
    def textureFormat(self) -> int: ...
    def updateBehavior(self) -> 'QOpenGLWidget.UpdateBehavior': ...
    def setUpdateBehavior(self, updateBehavior: 'QOpenGLWidget.UpdateBehavior') -> None: ...
    def paintEngine(self) -> typing.Optional[QtGui.QPaintEngine]: ...
    def metric(self, metric: QtGui.QPaintDevice.PaintDeviceMetric) -> int: ...
    def event(self, e: typing.Optional[QtCore.QEvent]) -> bool: ...
    def resizeEvent(self, e: typing.Optional[QtGui.QResizeEvent]) -> None: ...
    def paintEvent(self, e: typing.Optional[QtGui.QPaintEvent]) -> None: ...
    def paintGL(self) -> None: ...
    def resizeGL(self, w: int, h: int) -> None: ...
    def initializeGL(self) -> None: ...
    resized: typing.ClassVar[QtCore.pyqtSignal]
    aboutToResize: typing.ClassVar[QtCore.pyqtSignal]
    frameSwapped: typing.ClassVar[QtCore.pyqtSignal]
    aboutToCompose: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def grabFramebuffer(self) -> QtGui.QImage: ...
    @typing.overload
    def grabFramebuffer(self, targetBuffer: 'QOpenGLWidget.TargetBuffer') -> QtGui.QImage: ...
    @typing.overload
    def defaultFramebufferObject(self) -> int: ...
    @typing.overload
    def defaultFramebufferObject(self, targetBuffer: 'QOpenGLWidget.TargetBuffer') -> int: ...
    def context(self) -> typing.Optional[QtGui.QOpenGLContext]: ...
    def doneCurrent(self) -> None: ...
    @typing.overload
    def makeCurrent(self) -> None: ...
    @typing.overload
    def makeCurrent(self, targetBuffer: 'QOpenGLWidget.TargetBuffer') -> None: ...
    def isValid(self) -> bool: ...
    def format(self) -> QtGui.QSurfaceFormat: ...
    def setFormat(self, format: QtGui.QSurfaceFormat) -> None: ...
