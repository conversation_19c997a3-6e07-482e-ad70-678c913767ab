# 🎓 Math Re-teach App - Your Personal Learning Journey (Electron Edition)

A **spectacular**, modern math learning application built with Electron, featuring stunning animations, particle effects, and an engaging user experience that makes learning math actually fun! Designed for 8th & 9th grade students who want to learn at their own pace.

## ✨ Features

### 🎯 Student-Only Design Philosophy
- **No sign-ins or parent monitoring** - Your learning, your privacy
- **Self-paced learning** with optional timers for challenge
- **XP & rewards system** that motivates rather than punishes
- **Focus on fun, clarity, and smart feedback**

### 📚 Complete Curriculum
- **8th & 9th Grade Math Topics:**
  - Linear Equations & Systems
  - Quadratic Formula & Factoring
  - Functions & Polynomials
  - Pythagorean Theorem & Geometry
  - Exponents, Roots & Radicals
  - Inequalities & Rational Expressions
  - Probability & Statistics

### 🧠 Smart Learning Tools
- **📄 Cheat Sheets** - Quick reference guides for each topic
- **🧮 Practice Generator** - Unlimited problems with smart difficulty
- **💡 Hint System** - Get help when you're stuck
- **📝 Step-by-Step Mode** - Break down complex problems
- **🎯 Focus Sessions** - Optional timed learning sessions

### 🎮 Gamified Learning
- **⭐ XP System** - Earn experience points for every achievement
- **🏆 Leveling** - Progress through levels as you learn
- **🎖️ Achievements** - Unlock badges for milestones
- **🔥 Streaks** - Maintain daily learning habits
- **🎨 Unlockable Themes** - Customize your learning environment

### 📝 Assignment System
- **📋 Daily Homework** - Auto-generated assignments
- **⏰ Optional Timers** - 24-hour deadlines for extra challenge
- **🔁 Retry System** - Learn from mistakes without pressure
- **🔐 No Lockouts** - Skip lessons if you want - it's your path

### 🎨 Spectacular Modern UI
- **🌗 Premium Themes** - 5 stunning themes with smooth transitions
- **✨ Advanced Animations** - GSAP-powered 60fps animations
- **🎆 Particle Effects** - Success explosions, XP gains, level-up celebrations
- **🎨 Glass Morphism** - Modern translucent design elements
- **📱 Responsive Design** - Perfect on any screen size
- **🎭 Smooth Transitions** - Buttery smooth page transitions and interactions

### 🔊 Audio & Feedback
- **🎵 Sound Effects** - Encouraging audio feedback
- **🗣️ Text-to-Speech** - Optional voice guidance
- **🎉 Celebration Sounds** - Feel good about your progress

### 🕹️ Learning Modes
- **🌟 Free Mode** - No timers, just learn and vibe
- **⚡ XP Grind Mode** - Timed sessions with XP boosts
- **🔥 Challenge Mode** - Test-only runs with no hints

## 🚀 Getting Started

### Prerequisites
- Node.js 16 or higher
- Windows, macOS, or Linux

### Installation
1. Clone or download this repository
2. Open a terminal in the project directory
3. Install dependencies:
   ```bash
   npm install
   ```
4. Run the application:
   ```bash
   npm start
   ```

### Building for Distribution
```bash
# Build for current platform
npm run build

# Build for Windows
npm run build-win

# Build for macOS
npm run build-mac

# Build for Linux
npm run build-linux
```

### First Time Setup
1. The app will create a database and settings files automatically
2. Start with the "Lessons" tab to learn new concepts
3. Use "Practice" to reinforce your learning
4. Take "Quizzes" to test your knowledge
5. Check "Progress" to see your achievements

## 🎯 How to Use

### Learning Path
1. **Start with Lessons** - Read through the topic explanations
2. **Practice Problems** - Generate unlimited practice questions
3. **Use Hints & Steps** - Get help when you need it
4. **Take Quizzes** - Test your understanding
5. **Complete Homework** - Daily assignments for consistency
6. **Track Progress** - See your XP, level, and achievements

### Tips for Success
- **Set a daily goal** - Even 15 minutes makes a difference
- **Use the hint system** - Don't struggle alone
- **Celebrate small wins** - Every correct answer counts
- **Try different modes** - Find what works for you
- **Check your progress** - See how far you've come

## ⚙️ Settings & Customization

Access settings through the menu bar:
- **Themes** - Choose your favorite color scheme
- **Learning Mode** - Free, XP Grind, or Challenge
- **Audio** - Enable/disable sound effects and voice
- **Study Goals** - Set daily lesson targets
- **Focus Sessions** - Customize timer durations

## 🏆 Achievement System

Unlock achievements by:
- **First Steps** - Complete your first lesson
- **Getting Started** - Complete 3 lessons
- **Dedicated Learner** - Complete 10 lessons
- **Quiz Master** - Get 100% on a quiz
- **Week Warrior** - 7-day homework streak
- **Rising Star** - Reach Level 5
- **Math Champion** - Reach Level 10
- **Practice Makes Perfect** - Solve 50 practice problems

## 🎨 Available Themes

- **Dark Modern** - Sleek dark interface (default)
- **Light Modern** - Clean light interface
- **Neon Dark** - Cyberpunk-inspired (unlock with XP)
- **Ocean Blue** - Calming blue tones (unlock with XP)
- **Sunset Orange** - Warm orange palette (unlock with XP)

## 📊 Progress Tracking

The app tracks:
- **XP and Level** - Your overall progress
- **Lessons Completed** - Topics you've mastered
- **Practice Problems** - Questions you've solved
- **Quiz Scores** - Your test performance
- **Homework Streaks** - Daily consistency
- **Achievements** - Milestones reached

## 🔧 Technical Details

### Built With
- **Electron** - Cross-platform desktop app framework
- **HTML5/CSS3/JavaScript** - Modern web technologies
- **GSAP** - Professional-grade animations
- **Particles.js** - Stunning particle effects
- **IndexedDB/LocalStorage** - Client-side data persistence
- **Web Audio API** - Rich audio experience
- **Bootstrap** - Responsive UI components

### File Structure
```
src/
├── main.js                    # Electron main process
└── renderer/
    ├── index.html            # Main HTML file
    ├── css/
    │   ├── main.css         # Core styles
    │   ├── themes.css       # Theme system
    │   ├── animations.css   # Animation library
    │   └── components.css   # UI components
    └── js/
        ├── core/
        │   ├── app.js       # Main application
        │   ├── animations.js # GSAP animation manager
        │   ├── particles.js  # Particle system
        │   ├── audio.js     # Audio manager
        │   └── navigation.js # Navigation system
        ├── data/
        │   ├── storage.js   # Data persistence
        │   └── progress.js  # Progress tracking
        ├── pages/           # Page controllers
        └── ui/              # UI components
```

## 🤝 Contributing

This is a personal learning tool, but suggestions are welcome! Feel free to:
- Report bugs or issues
- Suggest new features
- Contribute additional math problems
- Improve the user interface

## 📝 License

This project is for educational use. Feel free to modify and adapt for your own learning needs.

## 🎉 Have Fun Learning!

Remember: This app is designed to make math learning enjoyable and stress-free. Take your time, celebrate your progress, and don't be afraid to make mistakes - that's how we learn!

Happy studying! 🚀📚✨
