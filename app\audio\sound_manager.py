# Sound Manager for Math Re-teach App
import os
import threading
from app.settings import get_setting

# Try to import audio libraries
try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False

class SoundManager:
    def __init__(self):
        self.sounds_enabled = get_setting('audio_fx', True)
        self.tts_enabled = get_setting('tts_enabled', False)
        self.pygame_initialized = False
        self.tts_engine = None
        
        if PYGAME_AVAILABLE and self.sounds_enabled:
            self.init_pygame()
        
        if TTS_AVAILABLE and self.tts_enabled:
            self.init_tts()

    def init_pygame(self):
        """Initialize pygame mixer for sound effects"""
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            self.pygame_initialized = True
        except pygame.error:
            self.pygame_initialized = False

    def init_tts(self):
        """Initialize text-to-speech engine"""
        try:
            self.tts_engine = pyttsx3.init()
            # Set properties
            self.tts_engine.setProperty('rate', 150)  # Speed of speech
            self.tts_engine.setProperty('volume', 0.7)  # Volume level (0.0 to 1.0)
        except:
            self.tts_engine = None

    def play_sound_effect(self, sound_type):
        """Play a sound effect"""
        if not self.sounds_enabled or not self.pygame_initialized:
            return
        
        # Generate simple tones using pygame
        try:
            if sound_type == "correct":
                self.play_tone(800, 0.2)  # High tone for correct
            elif sound_type == "incorrect":
                self.play_tone(300, 0.3)  # Low tone for incorrect
            elif sound_type == "level_up":
                self.play_level_up_sound()
            elif sound_type == "achievement":
                self.play_achievement_sound()
            elif sound_type == "click":
                self.play_tone(600, 0.1)  # Short click sound
        except:
            pass  # Fail silently if sound can't be played

    def play_tone(self, frequency, duration):
        """Generate and play a simple tone"""
        if not self.pygame_initialized:
            return
        
        def play_in_thread():
            try:
                sample_rate = 22050
                frames = int(duration * sample_rate)
                arr = []
                
                for i in range(frames):
                    time_point = float(i) / sample_rate
                    wave = 4096 * (0.5 * (1 + (time_point * frequency * 2 * 3.14159) % (2 * 3.14159) / 3.14159 - 1))
                    arr.append([int(wave), int(wave)])
                
                sound = pygame.sndarray.make_sound(arr)
                sound.play()
            except:
                pass
        
        # Play sound in separate thread to avoid blocking UI
        threading.Thread(target=play_in_thread, daemon=True).start()

    def play_level_up_sound(self):
        """Play a level up sound sequence"""
        def play_sequence():
            try:
                frequencies = [523, 659, 784, 1047]  # C, E, G, C (major chord)
                for freq in frequencies:
                    self.play_tone(freq, 0.15)
                    threading.Event().wait(0.1)
            except:
                pass
        
        threading.Thread(target=play_sequence, daemon=True).start()

    def play_achievement_sound(self):
        """Play an achievement unlock sound"""
        def play_sequence():
            try:
                # Triumphant sound sequence
                frequencies = [440, 554, 659, 880]  # A, C#, E, A
                for freq in frequencies:
                    self.play_tone(freq, 0.2)
                    threading.Event().wait(0.05)
            except:
                pass
        
        threading.Thread(target=play_sequence, daemon=True).start()

    def speak_text(self, text):
        """Use text-to-speech to speak text"""
        if not self.tts_enabled or not self.tts_engine:
            return
        
        def speak_in_thread():
            try:
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            except:
                pass
        
        threading.Thread(target=speak_in_thread, daemon=True).start()

    def play_encouragement(self):
        """Play encouraging sound and optionally speak encouragement"""
        self.play_sound_effect("correct")
        
        if self.tts_enabled:
            encouragements = [
                "Great job!",
                "Excellent work!",
                "You're doing amazing!",
                "Keep it up!",
                "Outstanding!",
                "Perfect!"
            ]
            import random
            message = random.choice(encouragements)
            self.speak_text(message)

    def play_motivation(self):
        """Play motivational sound for wrong answers"""
        self.play_sound_effect("incorrect")
        
        if self.tts_enabled:
            motivations = [
                "Don't give up!",
                "Try again!",
                "You can do this!",
                "Keep trying!",
                "Almost there!"
            ]
            import random
            message = random.choice(motivations)
            self.speak_text(message)

    def update_settings(self):
        """Update sound settings from app settings"""
        self.sounds_enabled = get_setting('audio_fx', True)
        self.tts_enabled = get_setting('tts_enabled', False)
        
        if TTS_AVAILABLE and self.tts_enabled and not self.tts_engine:
            self.init_tts()

    def cleanup(self):
        """Clean up audio resources"""
        if self.pygame_initialized:
            try:
                pygame.mixer.quit()
            except:
                pass
        
        if self.tts_engine:
            try:
                self.tts_engine.stop()
            except:
                pass

# Global sound manager instance
sound_manager = SoundManager()

# Convenience functions
def play_correct_sound():
    sound_manager.play_encouragement()

def play_incorrect_sound():
    sound_manager.play_motivation()

def play_level_up():
    sound_manager.play_sound_effect("level_up")

def play_achievement():
    sound_manager.play_sound_effect("achievement")

def play_click():
    sound_manager.play_sound_effect("click")

def speak(text):
    sound_manager.speak_text(text)
