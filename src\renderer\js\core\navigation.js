// Navigation Manager for Math Re-teach App
class NavigationManager {
    constructor() {
        this.currentPage = 'lessons';
        this.history = [];
        this.maxHistory = 10;
    }
    
    init() {
        this.setupEventListeners();
        console.log('🧭 Navigation Manager initialized');
    }
    
    setupEventListeners() {
        // Navigation menu items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                if (page) {
                    this.navigateTo(page);
                }
            });
        });
        
        // Browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.navigateTo(e.state.page, false);
            }
        });
    }
    
    navigateTo(page, addToHistory = true) {
        if (this.currentPage === page) return;
        
        const oldPage = this.currentPage;
        this.currentPage = page;
        
        // Add to history
        if (addToHistory) {
            this.addToHistory(oldPage);
        }
        
        // Update active navigation item
        this.setActivePage(page);
        
        // Dispatch navigation event
        const event = new CustomEvent('navigate', {
            detail: { page, oldPage }
        });
        document.dispatchEvent(event);
    }
    
    setActivePage(page) {
        // Update navigation menu
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.toggle('active', item.dataset.page === page);
        });
    }
    
    addToHistory(page) {
        this.history.push(page);
        if (this.history.length > this.maxHistory) {
            this.history.shift();
        }
    }
    
    goBack() {
        if (this.history.length > 0) {
            const previousPage = this.history.pop();
            this.navigateTo(previousPage, false);
        }
    }
    
    getCurrentPage() {
        return this.currentPage;
    }
    
    getHistory() {
        return [...this.history];
    }
}
