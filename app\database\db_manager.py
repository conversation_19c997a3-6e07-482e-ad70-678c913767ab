# Database Manager for Math Re-teach App
import sqlite3
import json
import os
from datetime import datetime
from contextlib import contextmanager

DB_PATH = os.path.join(os.path.dirname(__file__), '..', 'data', 'math_app.db')

class DatabaseManager:
    def __init__(self):
        self.ensure_data_directory()
        self.init_database()

    def ensure_data_directory(self):
        """Ensure the data directory exists"""
        data_dir = os.path.dirname(DB_PATH)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

    @contextmanager
    def get_connection(self):
        """Get a database connection with automatic cleanup"""
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()

    def init_database(self):
        """Initialize the database with required tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # User progress table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_progress (
                    id INTEGER PRIMARY KEY,
                    xp INTEGER DEFAULT 0,
                    level INTEGER DEFAULT 1,
                    practice_streak INTEGER DEFAULT 0,
                    last_activity_date TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Lessons table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lessons_completed (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    lesson_name TEXT NOT NULL,
                    completed_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    xp_earned INTEGER DEFAULT 0
                )
            ''')
            
            # Achievements table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS achievements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    achievement_id TEXT NOT NULL UNIQUE,
                    unlocked_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    xp_earned INTEGER DEFAULT 0
                )
            ''')
            
            # Quiz results table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS quiz_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    quiz_type TEXT NOT NULL,
                    score INTEGER NOT NULL,
                    max_score INTEGER NOT NULL,
                    percentage REAL NOT NULL,
                    completed_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    xp_earned INTEGER DEFAULT 0
                )
            ''')
            
            # Practice problems table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS practice_problems (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    topic TEXT NOT NULL,
                    question TEXT NOT NULL,
                    user_answer TEXT,
                    correct_answer TEXT NOT NULL,
                    is_correct BOOLEAN NOT NULL,
                    completed_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    xp_earned INTEGER DEFAULT 0
                )
            ''')
            
            # Homework assignments table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS homework_assignments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    assignment_id TEXT NOT NULL UNIQUE,
                    title TEXT NOT NULL,
                    problems_data TEXT NOT NULL,  -- JSON string
                    score INTEGER DEFAULT 0,
                    max_score INTEGER NOT NULL,
                    completed BOOLEAN DEFAULT FALSE,
                    due_date TEXT,
                    completed_at TEXT,
                    xp_earned INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Settings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS app_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Initialize default user progress if not exists
            cursor.execute('SELECT COUNT(*) FROM user_progress')
            if cursor.fetchone()[0] == 0:
                cursor.execute('''
                    INSERT INTO user_progress (xp, level, practice_streak, last_activity_date)
                    VALUES (0, 1, 0, ?)
                ''', (datetime.now().isoformat(),))
            
            conn.commit()

    def get_user_progress(self):
        """Get user progress data"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM user_progress WHERE id = 1')
            row = cursor.fetchone()
            
            if row:
                return {
                    'xp': row['xp'],
                    'level': row['level'],
                    'practice_streak': row['practice_streak'],
                    'last_activity_date': row['last_activity_date']
                }
            return {'xp': 0, 'level': 1, 'practice_streak': 0, 'last_activity_date': None}

    def update_user_progress(self, xp=None, level=None, practice_streak=None):
        """Update user progress"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            updates = []
            params = []
            
            if xp is not None:
                updates.append('xp = ?')
                params.append(xp)
            
            if level is not None:
                updates.append('level = ?')
                params.append(level)
            
            if practice_streak is not None:
                updates.append('practice_streak = ?')
                params.append(practice_streak)
            
            updates.append('last_activity_date = ?')
            params.append(datetime.now().isoformat())
            
            updates.append('updated_at = ?')
            params.append(datetime.now().isoformat())
            
            if updates:
                query = f'UPDATE user_progress SET {", ".join(updates)} WHERE id = 1'
                cursor.execute(query, params)
                conn.commit()

    def add_completed_lesson(self, lesson_name, xp_earned=10):
        """Add a completed lesson"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO lessons_completed (lesson_name, xp_earned)
                VALUES (?, ?)
            ''', (lesson_name, xp_earned))
            conn.commit()

    def get_completed_lessons(self):
        """Get list of completed lessons"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT lesson_name FROM lessons_completed ORDER BY completed_at')
            return [row['lesson_name'] for row in cursor.fetchall()]

    def add_achievement(self, achievement_id, xp_earned=50):
        """Add an unlocked achievement"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute('''
                    INSERT INTO achievements (achievement_id, xp_earned)
                    VALUES (?, ?)
                ''', (achievement_id, xp_earned))
                conn.commit()
                return True
            except sqlite3.IntegrityError:
                # Achievement already exists
                return False

    def get_achievements(self):
        """Get list of unlocked achievements"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT achievement_id FROM achievements ORDER BY unlocked_at')
            return [row['achievement_id'] for row in cursor.fetchall()]

    def add_quiz_result(self, quiz_type, score, max_score, xp_earned=0):
        """Add a quiz result"""
        percentage = (score / max_score * 100) if max_score > 0 else 0
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO quiz_results (quiz_type, score, max_score, percentage, xp_earned)
                VALUES (?, ?, ?, ?, ?)
            ''', (quiz_type, score, max_score, percentage, xp_earned))
            conn.commit()

    def add_practice_problem(self, topic, question, user_answer, correct_answer, is_correct, xp_earned=0):
        """Add a practice problem result"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO practice_problems (topic, question, user_answer, correct_answer, is_correct, xp_earned)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (topic, question, user_answer, correct_answer, is_correct, xp_earned))
            conn.commit()

    def get_practice_problem_count(self):
        """Get total number of practice problems solved"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM practice_problems')
            return cursor.fetchone()[0]

    def get_statistics(self):
        """Get comprehensive statistics"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Get basic progress
            progress = self.get_user_progress()
            
            # Get lesson count
            cursor.execute('SELECT COUNT(*) FROM lessons_completed')
            lessons_completed = cursor.fetchone()[0]
            
            # Get quiz count
            cursor.execute('SELECT COUNT(*) FROM quiz_results')
            quizzes_taken = cursor.fetchone()[0]
            
            # Get practice count
            cursor.execute('SELECT COUNT(*) FROM practice_problems')
            practice_problems = cursor.fetchone()[0]
            
            # Get achievement count
            cursor.execute('SELECT COUNT(*) FROM achievements')
            achievements_count = cursor.fetchone()[0]
            
            return {
                'total_xp': progress['xp'],
                'current_level': progress['level'],
                'lessons_completed': lessons_completed,
                'quizzes_passed': quizzes_taken,
                'tests_passed': 0,  # TODO: Implement tests
                'practice_problems_solved': practice_problems,
                'achievements_unlocked': achievements_count,
                'practice_streak': progress['practice_streak'],
                'total_achievements': 8  # Update based on ACHIEVEMENTS dict
            }

    def backup_data(self, backup_path):
        """Create a backup of the database"""
        try:
            import shutil
            shutil.copy2(DB_PATH, backup_path)
            return True
        except Exception:
            return False

    def restore_data(self, backup_path):
        """Restore data from a backup"""
        try:
            import shutil
            shutil.copy2(backup_path, DB_PATH)
            return True
        except Exception:
            return False

# Global database manager instance
db_manager = DatabaseManager()
