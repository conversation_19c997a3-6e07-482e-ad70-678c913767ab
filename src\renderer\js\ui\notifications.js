// Notification System for Math Re-teach App
class Notification {
    constructor(message, type = 'info', duration = 3000) {
        this.message = message;
        this.type = type;
        this.duration = duration;
        this.element = null;
        this.timeout = null;
    }
    
    show() {
        this.create();
        this.animate();
        this.autoHide();
    }
    
    create() {
        this.element = document.createElement('div');
        this.element.className = `notification ${this.type}`;
        
        const icon = this.getIcon();
        
        this.element.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">
                    <i class="${icon}"></i>
                    ${this.getTitle()}
                </div>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-body">
                ${this.message}
            </div>
        `;
        
        // Add close functionality
        const closeBtn = this.element.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => this.hide());
        
        // Add to container
        const container = document.getElementById('notification-container');
        container.appendChild(this.element);
    }
    
    getIcon() {
        switch (this.type) {
            case 'success': return 'fas fa-check-circle';
            case 'warning': return 'fas fa-exclamation-triangle';
            case 'error': return 'fas fa-times-circle';
            default: return 'fas fa-info-circle';
        }
    }
    
    getTitle() {
        switch (this.type) {
            case 'success': return 'Success';
            case 'warning': return 'Warning';
            case 'error': return 'Error';
            default: return 'Info';
        }
    }
    
    animate() {
        // Slide in animation
        gsap.fromTo(this.element,
            { x: 400, opacity: 0 },
            { x: 0, opacity: 1, duration: 0.5, ease: "power2.out" }
        );
    }
    
    hide() {
        if (this.timeout) {
            clearTimeout(this.timeout);
        }
        
        // Slide out animation
        gsap.to(this.element, {
            x: 400,
            opacity: 0,
            duration: 0.3,
            ease: "power2.in",
            onComplete: () => {
                if (this.element && this.element.parentNode) {
                    this.element.parentNode.removeChild(this.element);
                }
            }
        });
    }
    
    autoHide() {
        if (this.duration > 0) {
            this.timeout = setTimeout(() => {
                this.hide();
            }, this.duration);
        }
    }
}

// Global notification functions
window.showNotification = (message, type = 'info', duration = 3000) => {
    const notification = new Notification(message, type, duration);
    notification.show();
    return notification;
};

window.showSuccess = (message, duration = 3000) => {
    return showNotification(message, 'success', duration);
};

window.showWarning = (message, duration = 4000) => {
    return showNotification(message, 'warning', duration);
};

window.showError = (message, duration = 5000) => {
    return showNotification(message, 'error', duration);
};
