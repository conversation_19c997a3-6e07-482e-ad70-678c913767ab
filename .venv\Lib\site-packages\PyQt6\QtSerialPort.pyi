# The PEP 484 type hints stub file for the QtSerialPort module.
#
# Generated by SIP 6.12.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing, enum

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]


class QSerialPort(QtCore.QIODevice):

    class SerialPortError(enum.Enum):
        NoError = ... # type: QSerialPort.SerialPortError
        DeviceNotFoundError = ... # type: QSerialPort.SerialPortError
        PermissionError = ... # type: QSerialPort.SerialPortError
        OpenError = ... # type: QSerialPort.SerialPortError
        WriteError = ... # type: QSerialPort.SerialPortError
        ReadError = ... # type: QSerialPort.SerialPortError
        ResourceError = ... # type: QSerialPort.SerialPortError
        UnsupportedOperationError = ... # type: QSerialPort.SerialPortError
        TimeoutError = ... # type: QSerialPort.SerialPortError
        NotOpenError = ... # type: QSerialPort.SerialPortError
        UnknownError = ... # type: QSerialPort.SerialPortError

    class PinoutSignal(enum.Flag):
        NoSignal = ... # type: QSerialPort.PinoutSignal
        DataTerminalReadySignal = ... # type: QSerialPort.PinoutSignal
        DataCarrierDetectSignal = ... # type: QSerialPort.PinoutSignal
        DataSetReadySignal = ... # type: QSerialPort.PinoutSignal
        RingIndicatorSignal = ... # type: QSerialPort.PinoutSignal
        RequestToSendSignal = ... # type: QSerialPort.PinoutSignal
        ClearToSendSignal = ... # type: QSerialPort.PinoutSignal
        SecondaryTransmittedDataSignal = ... # type: QSerialPort.PinoutSignal
        SecondaryReceivedDataSignal = ... # type: QSerialPort.PinoutSignal

    class FlowControl(enum.Enum):
        NoFlowControl = ... # type: QSerialPort.FlowControl
        HardwareControl = ... # type: QSerialPort.FlowControl
        SoftwareControl = ... # type: QSerialPort.FlowControl

    class StopBits(enum.Enum):
        OneStop = ... # type: QSerialPort.StopBits
        OneAndHalfStop = ... # type: QSerialPort.StopBits
        TwoStop = ... # type: QSerialPort.StopBits

    class Parity(enum.Enum):
        NoParity = ... # type: QSerialPort.Parity
        EvenParity = ... # type: QSerialPort.Parity
        OddParity = ... # type: QSerialPort.Parity
        SpaceParity = ... # type: QSerialPort.Parity
        MarkParity = ... # type: QSerialPort.Parity

    class DataBits(enum.Enum):
        Data5 = ... # type: QSerialPort.DataBits
        Data6 = ... # type: QSerialPort.DataBits
        Data7 = ... # type: QSerialPort.DataBits
        Data8 = ... # type: QSerialPort.DataBits

    class BaudRate(enum.Enum):
        Baud1200 = ... # type: QSerialPort.BaudRate
        Baud2400 = ... # type: QSerialPort.BaudRate
        Baud4800 = ... # type: QSerialPort.BaudRate
        Baud9600 = ... # type: QSerialPort.BaudRate
        Baud19200 = ... # type: QSerialPort.BaudRate
        Baud38400 = ... # type: QSerialPort.BaudRate
        Baud57600 = ... # type: QSerialPort.BaudRate
        Baud115200 = ... # type: QSerialPort.BaudRate

    class Direction(enum.Flag):
        Input = ... # type: QSerialPort.Direction
        Output = ... # type: QSerialPort.Direction
        AllDirections = ... # type: QSerialPort.Direction

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, info: 'QSerialPortInfo', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    settingsRestoredOnCloseChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setSettingsRestoredOnClose(self, restore: bool) -> None: ...
    def settingsRestoredOnClose(self) -> bool: ...
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    breakEnabledChanged: typing.ClassVar[QtCore.pyqtSignal]
    def isBreakEnabled(self) -> bool: ...
    def handle(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def writeData(self, data: typing.Optional[PyQt6.sip.array[bytes]]) -> int: ...
    def readLineData(self, maxlen: int) -> bytes: ...
    def readData(self, maxlen: int) -> bytes: ...
    requestToSendChanged: typing.ClassVar[QtCore.pyqtSignal]
    dataTerminalReadyChanged: typing.ClassVar[QtCore.pyqtSignal]
    flowControlChanged: typing.ClassVar[QtCore.pyqtSignal]
    stopBitsChanged: typing.ClassVar[QtCore.pyqtSignal]
    parityChanged: typing.ClassVar[QtCore.pyqtSignal]
    dataBitsChanged: typing.ClassVar[QtCore.pyqtSignal]
    baudRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setBreakEnabled(self, enabled: bool = ...) -> bool: ...
    def waitForBytesWritten(self, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, msecs: int = ...) -> bool: ...
    def canReadLine(self) -> bool: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def isSequential(self) -> bool: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def readBufferSize(self) -> int: ...
    def clearError(self) -> None: ...
    def error(self) -> 'QSerialPort.SerialPortError': ...
    def clear(self, dir: 'QSerialPort.Direction' = ...) -> bool: ...
    def flush(self) -> bool: ...
    def pinoutSignals(self) -> 'QSerialPort.PinoutSignal': ...
    def isRequestToSend(self) -> bool: ...
    def setRequestToSend(self, set: bool) -> bool: ...
    def isDataTerminalReady(self) -> bool: ...
    def setDataTerminalReady(self, set: bool) -> bool: ...
    def flowControl(self) -> 'QSerialPort.FlowControl': ...
    def setFlowControl(self, flow: 'QSerialPort.FlowControl') -> bool: ...
    def stopBits(self) -> 'QSerialPort.StopBits': ...
    def setStopBits(self, stopBits: 'QSerialPort.StopBits') -> bool: ...
    def parity(self) -> 'QSerialPort.Parity': ...
    def setParity(self, parity: 'QSerialPort.Parity') -> bool: ...
    def dataBits(self) -> 'QSerialPort.DataBits': ...
    def setDataBits(self, dataBits: 'QSerialPort.DataBits') -> bool: ...
    def baudRate(self, dir: 'QSerialPort.Direction' = ...) -> int: ...
    def setBaudRate(self, baudRate: int, dir: 'QSerialPort.Direction' = ...) -> bool: ...
    def close(self) -> None: ...
    def open(self, mode: QtCore.QIODeviceBase.OpenModeFlag) -> bool: ...
    def setPort(self, info: 'QSerialPortInfo') -> None: ...
    def portName(self) -> str: ...
    def setPortName(self, name: typing.Optional[str]) -> None: ...


class QSerialPortInfo(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, port: QSerialPort) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSerialPortInfo') -> None: ...

    def serialNumber(self) -> str: ...
    def isNull(self) -> bool: ...
    @staticmethod
    def availablePorts() -> list['QSerialPortInfo']: ...
    @staticmethod
    def standardBaudRates() -> list[int]: ...
    def hasProductIdentifier(self) -> bool: ...
    def hasVendorIdentifier(self) -> bool: ...
    def productIdentifier(self) -> int: ...
    def vendorIdentifier(self) -> int: ...
    def manufacturer(self) -> str: ...
    def description(self) -> str: ...
    def systemLocation(self) -> str: ...
    def portName(self) -> str: ...
    def swap(self, other: 'QSerialPortInfo') -> None: ...
