# Utility functions for countdown timers and settings
from PyQt5.QtCore import QTimer, QDateTime

class CountdownTimer:
    def __init__(self, duration_seconds, update_callback, finish_callback):
        self.duration = duration_seconds
        self.update_callback = update_callback
        self.finish_callback = finish_callback
        self.timer = QTimer()
        self.timer.timeout.connect(self._tick)
        self.end_time = QDateTime.currentDateTime().addSecs(self.duration)

    def start(self):
        self.timer.start(1000)
        self._tick()

    def _tick(self):
        now = QDateTime.currentDateTime()
        remaining = now.secsTo(self.end_time)
        if remaining > 0:
            self.update_callback(remaining)
        else:
            self.timer.stop()
            self.finish_callback()
