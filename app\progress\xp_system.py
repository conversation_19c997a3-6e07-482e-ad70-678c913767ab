# XP and progress system for the app
# Stores XP, levels, achievements, and lesson progress locally
import json
import os

SAVE_FILE = os.path.join(os.path.dirname(__file__), '..', 'progress', 'save_data.json')

DEFAULT_DATA = {
    "xp": 0,
    "level": 1,
    "achievements": [],
    "lessons_completed": [],
    "quizzes_passed": [],
    "tests_passed": [],
    "practice_streak": 0
}

def load_progress():
    if os.path.exists(SAVE_FILE):
        with open(SAVE_FILE, 'r') as f:
            return json.load(f)
    return DEFAULT_DATA.copy()

def save_progress(data):
    with open(SAVE_FILE, 'w') as f:
        json.dump(data, f, indent=2)

def add_xp(amount):
    data = load_progress()
    data["xp"] += amount
    # Level up for every 100 XP
    while data["xp"] >= data["level"] * 100:
        data["xp"] -= data["level"] * 100
        data["level"] += 1
    save_progress(data)

def complete_lesson(lesson):
    data = load_progress()
    if lesson not in data["lessons_completed"]:
        data["lessons_completed"].append(lesson)
        add_xp(10)
        save_progress(data)

def unlock_achievement(name):
    data = load_progress()
    if name not in data["achievements"]:
        data["achievements"].append(name)
        add_xp(50)
        save_progress(data)
        return True
    return False

# Achievement definitions
ACHIEVEMENTS = {
    "first_lesson": {
        "name": "First Steps",
        "description": "Complete your first lesson",
        "icon": "🎯",
        "xp_reward": 25
    },
    "lesson_streak_3": {
        "name": "Getting Started",
        "description": "Complete 3 lessons",
        "icon": "📚",
        "xp_reward": 50
    },
    "lesson_streak_10": {
        "name": "Dedicated Learner",
        "description": "Complete 10 lessons",
        "icon": "🏆",
        "xp_reward": 100
    },
    "perfect_quiz": {
        "name": "Quiz Master",
        "description": "Get 100% on a quiz",
        "icon": "🎯",
        "xp_reward": 75
    },
    "homework_streak_7": {
        "name": "Week Warrior",
        "description": "Complete homework for 7 days straight",
        "icon": "🔥",
        "xp_reward": 150
    },
    "level_5": {
        "name": "Rising Star",
        "description": "Reach Level 5",
        "icon": "⭐",
        "xp_reward": 100
    },
    "level_10": {
        "name": "Math Champion",
        "description": "Reach Level 10",
        "icon": "👑",
        "xp_reward": 200
    },
    "practice_master": {
        "name": "Practice Makes Perfect",
        "description": "Solve 50 practice problems",
        "icon": "🧮",
        "xp_reward": 125
    }
}

def check_achievements():
    """Check and unlock any new achievements"""
    data = load_progress()
    new_achievements = []

    # Check lesson-based achievements
    lessons_completed = len(data.get("lessons_completed", []))
    if lessons_completed >= 1 and "first_lesson" not in data["achievements"]:
        if unlock_achievement("first_lesson"):
            new_achievements.append("first_lesson")

    if lessons_completed >= 3 and "lesson_streak_3" not in data["achievements"]:
        if unlock_achievement("lesson_streak_3"):
            new_achievements.append("lesson_streak_3")

    if lessons_completed >= 10 and "lesson_streak_10" not in data["achievements"]:
        if unlock_achievement("lesson_streak_10"):
            new_achievements.append("lesson_streak_10")

    # Check level-based achievements
    level = data.get("level", 1)
    if level >= 5 and "level_5" not in data["achievements"]:
        if unlock_achievement("level_5"):
            new_achievements.append("level_5")

    if level >= 10 and "level_10" not in data["achievements"]:
        if unlock_achievement("level_10"):
            new_achievements.append("level_10")

    # Check practice-based achievements
    practice_count = data.get("practice_problems_solved", 0)
    if practice_count >= 50 and "practice_master" not in data["achievements"]:
        if unlock_achievement("practice_master"):
            new_achievements.append("practice_master")

    return new_achievements

def add_practice_problem():
    """Increment practice problems solved counter"""
    data = load_progress()
    data["practice_problems_solved"] = data.get("practice_problems_solved", 0) + 1
    save_progress(data)
    check_achievements()

def get_level_progress():
    """Get current level progress information"""
    data = load_progress()
    level = data.get("level", 1)
    xp = data.get("xp", 0)

    # XP required for current level
    xp_for_current_level = level * 100

    # XP progress within current level
    level_progress = (xp / xp_for_current_level) * 100 if xp_for_current_level > 0 else 0

    return {
        "level": level,
        "xp": xp,
        "xp_for_next_level": xp_for_current_level,
        "progress_percent": min(level_progress, 100),
        "xp_to_next_level": max(0, xp_for_current_level - xp)
    }

def get_statistics():
    """Get comprehensive learning statistics"""
    data = load_progress()

    return {
        "total_xp": data.get("xp", 0),
        "current_level": data.get("level", 1),
        "lessons_completed": len(data.get("lessons_completed", [])),
        "quizzes_passed": len(data.get("quizzes_passed", [])),
        "tests_passed": len(data.get("tests_passed", [])),
        "practice_problems_solved": data.get("practice_problems_solved", 0),
        "achievements_unlocked": len(data.get("achievements", [])),
        "practice_streak": data.get("practice_streak", 0),
        "total_achievements": len(ACHIEVEMENTS)
    }
