# XP and progress system for the app
# Stores XP, levels, achievements, and lesson progress locally
import json
import os

SAVE_FILE = os.path.join(os.path.dirname(__file__), '..', 'progress', 'save_data.json')

DEFAULT_DATA = {
    "xp": 0,
    "level": 1,
    "achievements": [],
    "lessons_completed": [],
    "quizzes_passed": [],
    "tests_passed": [],
    "practice_streak": 0
}

def load_progress():
    if os.path.exists(SAVE_FILE):
        with open(SAVE_FILE, 'r') as f:
            return json.load(f)
    return DEFAULT_DATA.copy()

def save_progress(data):
    with open(SAVE_FILE, 'w') as f:
        json.dump(data, f, indent=2)

def add_xp(amount):
    data = load_progress()
    data["xp"] += amount
    # Level up for every 100 XP
    while data["xp"] >= data["level"] * 100:
        data["xp"] -= data["level"] * 100
        data["level"] += 1
    save_progress(data)

def complete_lesson(lesson):
    data = load_progress()
    if lesson not in data["lessons_completed"]:
        data["lessons_completed"].append(lesson)
        add_xp(10)
        save_progress(data)

def unlock_achievement(name):
    data = load_progress()
    if name not in data["achievements"]:
        data["achievements"].append(name)
        add_xp(50)
        save_progress(data)
