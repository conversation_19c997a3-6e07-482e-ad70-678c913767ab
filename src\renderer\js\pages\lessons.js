// Lessons Page for Math Re-teach App
class LessonsPage {
    constructor() {
        this.lessons = [
            {
                id: 'linear-equations',
                title: 'Linear Equations',
                description: 'Learn to solve equations with one variable',
                icon: '📐',
                difficulty: 'Beginner',
                estimatedTime: '15 min',
                completed: false
            },
            {
                id: 'quadratic-formula',
                title: 'Quadratic Formula',
                description: 'Master the quadratic formula and its applications',
                icon: '🔢',
                difficulty: 'Intermediate',
                estimatedTime: '20 min',
                completed: false
            },
            {
                id: 'pythagorean-theorem',
                title: 'Pythagorean Theorem',
                description: 'Understand right triangles and the famous theorem',
                icon: '📐',
                difficulty: 'Beginner',
                estimatedTime: '18 min',
                completed: false
            },
            {
                id: 'factoring',
                title: 'Factoring Polynomials',
                description: 'Learn different methods to factor expressions',
                icon: '🧮',
                difficulty: 'Intermediate',
                estimatedTime: '25 min',
                completed: false
            },
            {
                id: 'systems-equations',
                title: 'Systems of Equations',
                description: 'Solve multiple equations simultaneously',
                icon: '⚖️',
                difficulty: 'Intermediate',
                estimatedTime: '22 min',
                completed: false
            },
            {
                id: 'exponents-roots',
                title: 'Exponents and Roots',
                description: 'Master powers, roots, and radical expressions',
                icon: '√',
                difficulty: 'Beginner',
                estimatedTime: '16 min',
                completed: false
            }
        ];
    }
    
    init() {
        console.log('📚 Lessons Page initialized');
    }
    
    show() {
        console.log('📚 Showing Lessons Page');
    }
    
    render() {
        const container = document.createElement('div');
        container.className = 'page';
        container.id = 'lessons-page';
        
        container.innerHTML = `
            <div class="page-header">
                <h1><i class="fas fa-book"></i> Lessons</h1>
                <p>Master the fundamentals with our comprehensive lessons</p>
            </div>
            <div class="page-body">
                <div class="lessons-grid grid grid-3">
                    ${this.lessons.map(lesson => this.renderLessonCard(lesson)).join('')}
                </div>
            </div>
        `;
        
        // Add event listeners
        this.addEventListeners(container);
        
        return container;
    }
    
    renderLessonCard(lesson) {
        const completedClass = lesson.completed ? 'completed' : '';
        const difficultyColor = this.getDifficultyColor(lesson.difficulty);
        
        return `
            <div class="lesson-card card hover-lift ${completedClass}" data-lesson-id="${lesson.id}">
                <div class="lesson-icon">
                    ${lesson.icon}
                </div>
                <div class="lesson-content">
                    <h3 class="lesson-title">${lesson.title}</h3>
                    <p class="lesson-description">${lesson.description}</p>
                    <div class="lesson-meta">
                        <span class="lesson-difficulty" style="color: ${difficultyColor}">
                            <i class="fas fa-signal"></i>
                            ${lesson.difficulty}
                        </span>
                        <span class="lesson-time">
                            <i class="fas fa-clock"></i>
                            ${lesson.estimatedTime}
                        </span>
                    </div>
                </div>
                <div class="lesson-actions">
                    <button class="btn btn-primary lesson-start-btn" data-lesson-id="${lesson.id}">
                        ${lesson.completed ? 'Review' : 'Start'} Lesson
                    </button>
                </div>
                ${lesson.completed ? '<div class="lesson-completed-badge"><i class="fas fa-check"></i></div>' : ''}
            </div>
        `;
    }
    
    getDifficultyColor(difficulty) {
        switch (difficulty.toLowerCase()) {
            case 'beginner': return '#4caf50';
            case 'intermediate': return '#ff9800';
            case 'advanced': return '#f44336';
            default: return '#2196f3';
        }
    }
    
    addEventListeners(container) {
        // Lesson start buttons
        container.querySelectorAll('.lesson-start-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const lessonId = btn.dataset.lessonId;
                this.startLesson(lessonId);
            });
        });
        
        // Lesson cards (for preview)
        container.querySelectorAll('.lesson-card').forEach(card => {
            card.addEventListener('click', () => {
                const lessonId = card.dataset.lessonId;
                this.previewLesson(lessonId);
            });
        });
    }
    
    startLesson(lessonId) {
        const lesson = this.lessons.find(l => l.id === lessonId);
        if (!lesson) return;
        
        console.log(`Starting lesson: ${lesson.title}`);
        
        // Play click sound
        if (window.MathApp && window.MathApp.audio) {
            window.MathApp.audio.playClick();
        }
        
        // Show lesson modal or navigate to lesson view
        this.showLessonModal(lesson);
    }
    
    previewLesson(lessonId) {
        const lesson = this.lessons.find(l => l.id === lessonId);
        if (!lesson) return;
        
        console.log(`Previewing lesson: ${lesson.title}`);
        
        // Show preview tooltip or quick info
        showNotification(`Preview: ${lesson.title} - ${lesson.description}`, 'info', 2000);
    }
    
    showLessonModal(lesson) {
        // Create lesson modal
        const modal = document.createElement('div');
        modal.className = 'modal show';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2><i class="fas fa-book"></i> ${lesson.title}</h2>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="lesson-content-area">
                        <div class="lesson-intro">
                            <div class="lesson-icon-large">${lesson.icon}</div>
                            <h3>Welcome to ${lesson.title}</h3>
                            <p>${lesson.description}</p>
                            <div class="lesson-stats">
                                <div class="stat">
                                    <i class="fas fa-signal"></i>
                                    <span>Difficulty: ${lesson.difficulty}</span>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-clock"></i>
                                    <span>Time: ${lesson.estimatedTime}</span>
                                </div>
                            </div>
                        </div>
                        <div class="lesson-objectives">
                            <h4>Learning Objectives</h4>
                            <ul>
                                <li>Understand the fundamental concepts</li>
                                <li>Practice with guided examples</li>
                                <li>Apply knowledge to solve problems</li>
                                <li>Master the techniques through repetition</li>
                            </ul>
                        </div>
                        <div class="lesson-actions-modal">
                            <button class="btn btn-secondary" id="lesson-preview-btn">
                                <i class="fas fa-eye"></i>
                                Preview Content
                            </button>
                            <button class="btn btn-primary" id="lesson-begin-btn">
                                <i class="fas fa-play"></i>
                                Begin Lesson
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add event listeners
        modal.querySelector('.modal-close').addEventListener('click', () => {
            this.closeModal(modal);
        });
        
        modal.querySelector('#lesson-preview-btn').addEventListener('click', () => {
            this.previewLessonContent(lesson);
        });
        
        modal.querySelector('#lesson-begin-btn').addEventListener('click', () => {
            this.beginLesson(lesson);
            this.closeModal(modal);
        });
        
        // Close on backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modal);
            }
        });
        
        // Animate modal entrance
        gsap.fromTo(modal.querySelector('.modal-content'),
            { scale: 0.8, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.3, ease: "power2.out" }
        );
    }
    
    closeModal(modal) {
        gsap.to(modal.querySelector('.modal-content'), {
            scale: 0.8,
            opacity: 0,
            duration: 0.2,
            ease: "power2.in",
            onComplete: () => {
                modal.remove();
            }
        });
    }
    
    previewLessonContent(lesson) {
        showNotification(`Preview content for ${lesson.title} - Coming soon!`, 'info');
    }
    
    beginLesson(lesson) {
        console.log(`Beginning lesson: ${lesson.title}`);
        
        // Mark as started
        lesson.started = true;
        
        // Show success notification
        showSuccess(`Started lesson: ${lesson.title}`);
        
        // Play success sound
        if (window.MathApp && window.MathApp.audio) {
            window.MathApp.audio.playCorrect();
        }
        
        // Award XP for starting lesson
        if (window.MathApp && window.MathApp.progress) {
            window.MathApp.progress.addXP(5, `Started lesson: ${lesson.title}`);
        }
        
        // TODO: Navigate to actual lesson content
        // For now, simulate lesson completion after a delay
        setTimeout(() => {
            this.completeLesson(lesson);
        }, 3000);
    }
    
    completeLesson(lesson) {
        lesson.completed = true;
        
        // Show completion notification
        showSuccess(`Completed lesson: ${lesson.title}!`);
        
        // Award XP for completion
        if (window.MathApp && window.MathApp.progress) {
            window.MathApp.progress.completeLesson(lesson.id, 100, 900); // 100% score, 15 minutes
        }
        
        // Update UI
        this.updateLessonCard(lesson);
        
        // Play level up sound
        if (window.MathApp && window.MathApp.audio) {
            window.MathApp.audio.playLevelUp();
        }
        
        // Create celebration particles
        if (window.MathApp && window.MathApp.particles) {
            const centerX = window.innerWidth / 2;
            const centerY = window.innerHeight / 2;
            window.MathApp.particles.createSuccessExplosion(centerX, centerY, 1.5);
        }
    }
    
    updateLessonCard(lesson) {
        const card = document.querySelector(`[data-lesson-id="${lesson.id}"]`);
        if (card) {
            card.classList.add('completed');
            
            // Add completed badge
            if (!card.querySelector('.lesson-completed-badge')) {
                const badge = document.createElement('div');
                badge.className = 'lesson-completed-badge';
                badge.innerHTML = '<i class="fas fa-check"></i>';
                card.appendChild(badge);
            }
            
            // Update button text
            const btn = card.querySelector('.lesson-start-btn');
            if (btn) {
                btn.textContent = 'Review Lesson';
            }
            
            // Animate completion
            gsap.fromTo(card,
                { scale: 1 },
                { scale: 1.05, duration: 0.2, yoyo: true, repeat: 1, ease: "power2.inOut" }
            );
        }
    }
}
