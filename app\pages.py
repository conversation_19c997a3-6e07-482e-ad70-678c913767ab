# This file contains all the page classes for the main app
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QLabel, QListWidget, QTextEdit,
                            QPushButton, QStackedWidget, QHBoxLayout, QScrollArea,
                            QFrame, QGridLayout, QProgressBar, QButtonGroup, QRadioButton,
                            QSpinBox, QCheckBox, QComboBox, QSlider, QGroupBox, QLineEdit)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPoint
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor
from app.lessons.topics import LESSONS
from app.cheatsheets.cheats import CHEAT_SHEETS
from app.quizzes.questions import QUESTIONS
from app.homework.assignments import HOMEWORK
from app.progress.xp_system import (load_progress, add_xp, complete_lesson,
                                   get_statistics, get_level_progress, ACHIEVEMENTS,
                                   check_achievements, add_practice_problem)
from app.practice_generator import generate_practice, PRACTICE_GENERATORS
from app.audio.sound_manager import play_correct_sound, play_incorrect_sound, play_level_up, play_achievement
from app.ui.premium_components import PremiumButton, GlassCard, AnimatedProgressBar, FloatingNotification
from app.ui.animations import animation_manager, AdvancedParticleSystem
import random

class LessonsPage(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Lesson list
        self.list = QListWidget()
        self.list.setMaximumWidth(300)
        for lesson in LESSONS:
            self.list.addItem(f"📖 {lesson['title']}")

        # Lesson content
        content_widget = QWidget()
        content_layout = QVBoxLayout()

        # Title
        self.title_label = QLabel()
        self.title_label.setFont(QFont('Segoe UI', 20, QFont.Weight.Bold))
        self.title_label.setProperty("class", "header")

        # Content
        self.text = QTextEdit()
        self.text.setReadOnly(True)
        self.text.setFont(QFont('Segoe UI', 14))

        # Complete lesson button
        self.complete_btn = PremiumButton("✅ Mark as Complete", "", "success")
        self.complete_btn.clicked.connect(self.complete_current_lesson)

        content_layout.addWidget(self.title_label)
        content_layout.addWidget(self.text)
        content_layout.addWidget(self.complete_btn)
        content_widget.setLayout(content_layout)

        # Connect signals
        self.list.currentRowChanged.connect(self.show_lesson)

        layout.addWidget(self.list)
        layout.addWidget(content_widget)
        self.setLayout(layout)

        if LESSONS:
            self.list.setCurrentRow(0)

    def show_lesson(self, idx):
        if 0 <= idx < len(LESSONS):
            lesson = LESSONS[idx]
            self.title_label.setText(lesson["title"])
            self.text.setText(lesson["content"])
            self.current_lesson = lesson["title"]

    def complete_current_lesson(self):
        if hasattr(self, 'current_lesson'):
            complete_lesson(self.current_lesson)
            self.complete_btn.setText("✅ Completed!")
            self.complete_btn.setEnabled(False)
            QTimer.singleShot(2000, lambda: self.reset_complete_button())

    def reset_complete_button(self):
        self.complete_btn.setText("✅ Mark as Complete")
        self.complete_btn.setEnabled(True)

class CheatSheetsPage(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Cheat sheet list
        self.list = QListWidget()
        self.list.setMaximumWidth(300)
        for cheat in CHEAT_SHEETS:
            self.list.addItem(f"📄 {cheat['title']}")

        # Content area
        content_widget = QWidget()
        content_layout = QVBoxLayout()

        # Title
        self.title_label = QLabel()
        self.title_label.setFont(QFont('Segoe UI', 20, QFont.Weight.Bold))
        self.title_label.setProperty("class", "header")

        # Content
        self.text = QTextEdit()
        self.text.setReadOnly(True)
        self.text.setFont(QFont('Segoe UI', 14))

        content_layout.addWidget(self.title_label)
        content_layout.addWidget(self.text)
        content_widget.setLayout(content_layout)

        # Connect signals
        self.list.currentRowChanged.connect(self.show_cheat)

        layout.addWidget(self.list)
        layout.addWidget(content_widget)
        self.setLayout(layout)

        if CHEAT_SHEETS:
            self.list.setCurrentRow(0)

    def show_cheat(self, idx):
        if 0 <= idx < len(CHEAT_SHEETS):
            cheat = CHEAT_SHEETS[idx]
            self.title_label.setText(cheat["title"])
            self.text.setText(cheat["content"])

class PracticePage(QWidget):
    def __init__(self):
        super().__init__()
        self.current_problem = None
        self.show_hints = False
        self.show_steps = False

        # Create particle system for this page
        self.particle_system = None

        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Header
        header_layout = QHBoxLayout()
        title = QLabel("🧮 Practice Problems")
        title.setFont(QFont('Segoe UI', 24, QFont.Weight.Bold))
        title.setProperty("class", "header")

        # Topic selector
        self.topic_combo = QComboBox()
        self.topic_combo.addItems(list(PRACTICE_GENERATORS.keys()))
        self.topic_combo.currentTextChanged.connect(self.generate_new_problem)

        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(QLabel("Topic:"))
        header_layout.addWidget(self.topic_combo)

        # Problem area
        problem_frame = QFrame()
        problem_frame.setProperty("class", "card")
        problem_layout = QVBoxLayout()

        self.problem_label = QLabel("Click 'New Problem' to start!")
        self.problem_label.setFont(QFont('Segoe UI', 16))
        self.problem_label.setWordWrap(True)

        # Answer input
        answer_layout = QHBoxLayout()
        answer_layout.addWidget(QLabel("Your Answer:"))
        self.answer_input = QLineEdit()
        self.answer_input.setFont(QFont('Segoe UI', 14))
        self.answer_input.returnPressed.connect(self.check_answer)
        answer_layout.addWidget(self.answer_input)

        # Buttons with premium styling
        button_layout = QHBoxLayout()
        self.new_problem_btn = PremiumButton("🎲 New Problem", "", "primary")
        self.new_problem_btn.clicked.connect(self.generate_new_problem)

        self.check_btn = PremiumButton("✅ Check Answer", "", "success")
        self.check_btn.clicked.connect(self.check_answer)

        self.hint_btn = PremiumButton("💡 Show Hint", "", "warning")
        self.hint_btn.clicked.connect(self.toggle_hints)

        self.steps_btn = PremiumButton("📝 Show Steps", "", "secondary")
        self.steps_btn.clicked.connect(self.toggle_steps)

        button_layout.addWidget(self.new_problem_btn)
        button_layout.addWidget(self.check_btn)
        button_layout.addWidget(self.hint_btn)
        button_layout.addWidget(self.steps_btn)

        # Feedback area
        self.feedback_label = QLabel("")
        self.feedback_label.setFont(QFont('Segoe UI', 14))
        self.feedback_label.setWordWrap(True)

        # Hints and steps area
        self.hints_text = QTextEdit()
        self.hints_text.setMaximumHeight(150)
        self.hints_text.setVisible(False)

        self.steps_text = QTextEdit()
        self.steps_text.setMaximumHeight(200)
        self.steps_text.setVisible(False)

        # Add to layout
        problem_layout.addWidget(self.problem_label)
        problem_layout.addLayout(answer_layout)
        problem_layout.addLayout(button_layout)
        problem_layout.addWidget(self.feedback_label)
        problem_layout.addWidget(self.hints_text)
        problem_layout.addWidget(self.steps_text)
        problem_frame.setLayout(problem_layout)

        layout.addLayout(header_layout)
        layout.addWidget(problem_frame)
        layout.addStretch()
        self.setLayout(layout)

        # Initialize particle system after layout is set
        QTimer.singleShot(100, self.init_particle_system)

    def init_particle_system(self):
        """Initialize particle system for this page"""
        self.particle_system = AdvancedParticleSystem(self)
        self.particle_system.show()

    def generate_new_problem(self):
        topic = self.topic_combo.currentText()
        if topic in PRACTICE_GENERATORS:
            self.current_problem = generate_practice(topic)
            self.problem_label.setText(self.current_problem["question"])
            self.answer_input.clear()
            self.feedback_label.clear()
            self.hints_text.clear()
            self.steps_text.clear()
            self.show_hints = False
            self.show_steps = False
            self.hints_text.setVisible(False)
            self.steps_text.setVisible(False)
            self.hint_btn.setText("💡 Show Hint")
            self.steps_btn.setText("📝 Show Steps")

    def check_answer(self):
        if not self.current_problem:
            return

        user_answer = self.answer_input.text().strip()
        correct_answer = self.current_problem["answer"]

        if user_answer.lower() == correct_answer.lower():
            self.feedback_label.setText("🎉 Correct! Great job!")
            self.feedback_label.setStyleSheet("color: #4caf50; font-weight: bold;")
            add_xp(5)  # Award XP for correct answer
            add_practice_problem()  # Track practice problem completion
            play_correct_sound()  # Play success sound

            # Create success particle effect
            if self.particle_system:
                button_pos = self.check_btn.mapTo(self, self.check_btn.rect().center())
                self.particle_system.create_success_explosion(button_pos, intensity=0.8)

            # Bounce animation for feedback
            animation_manager.create_bounce_animation(self.feedback_label, duration=500).start()

        else:
            self.feedback_label.setText(f"❌ Not quite. The correct answer is: {correct_answer}")
            self.feedback_label.setStyleSheet("color: #f44336; font-weight: bold;")
            play_incorrect_sound()  # Play try again sound

            # Shake animation for wrong answer
            original_pos = self.answer_input.pos()
            shake_anim = animation_manager.create_slide_animation(
                self.answer_input, duration=100,
                start_pos=QPoint(original_pos.x() - 5, original_pos.y()),
                end_pos=QPoint(original_pos.x() + 5, original_pos.y())
            )
            shake_anim.start()

    def toggle_hints(self):
        if not self.current_problem:
            return

        self.show_hints = not self.show_hints
        if self.show_hints:
            hints = "\n".join(self.current_problem.get("hints", []))
            self.hints_text.setText(hints)
            self.hints_text.setVisible(True)
            self.hint_btn.setText("🙈 Hide Hint")
        else:
            self.hints_text.setVisible(False)
            self.hint_btn.setText("💡 Show Hint")

    def toggle_steps(self):
        if not self.current_problem:
            return

        self.show_steps = not self.show_steps
        if self.show_steps:
            steps = "\n".join(self.current_problem.get("steps", []))
            self.steps_text.setText(steps)
            self.steps_text.setVisible(True)
            self.steps_btn.setText("🙈 Hide Steps")
        else:
            self.steps_text.setVisible(False)
            self.steps_btn.setText("📝 Show Steps")

class QuizPage(QWidget):
    def __init__(self):
        super().__init__()
        self.current_question = 0
        self.score = 0
        self.quiz_questions = []
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Header
        header_layout = QHBoxLayout()
        title = QLabel("❓ Quiz Time")
        title.setFont(QFont('Segoe UI', 24, QFont.Weight.Bold))
        title.setProperty("class", "header")

        self.score_label = QLabel("Score: 0/0")
        self.score_label.setFont(QFont('Segoe UI', 16))

        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(self.score_label)

        # Quiz area
        quiz_frame = QFrame()
        quiz_frame.setProperty("class", "card")
        quiz_layout = QVBoxLayout()

        self.question_label = QLabel("Click 'Start Quiz' to begin!")
        self.question_label.setFont(QFont('Segoe UI', 16))
        self.question_label.setWordWrap(True)

        # Answer options
        self.answer_group = QButtonGroup()
        self.answer_buttons = []
        for i in range(4):
            btn = QRadioButton()
            btn.setFont(QFont('Segoe UI', 14))
            self.answer_buttons.append(btn)
            self.answer_group.addButton(btn, i)
            quiz_layout.addWidget(btn)

        # Buttons with premium styling
        button_layout = QHBoxLayout()
        self.start_btn = PremiumButton("🚀 Start Quiz", "", "primary")
        self.start_btn.clicked.connect(self.start_quiz)

        self.next_btn = PremiumButton("➡️ Next Question", "", "success")
        self.next_btn.clicked.connect(self.next_question)
        self.next_btn.setVisible(False)

        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.next_btn)

        quiz_layout.addWidget(self.question_label)
        quiz_layout.addLayout(button_layout)
        quiz_frame.setLayout(quiz_layout)

        layout.addLayout(header_layout)
        layout.addWidget(quiz_frame)
        layout.addStretch()
        self.setLayout(layout)

    def start_quiz(self):
        self.quiz_questions = random.sample(QUESTIONS, min(5, len(QUESTIONS)))
        self.current_question = 0
        self.score = 0
        self.start_btn.setVisible(False)
        self.next_btn.setVisible(True)
        self.show_question()

    def show_question(self):
        if self.current_question < len(self.quiz_questions):
            question = self.quiz_questions[self.current_question]
            self.question_label.setText(f"Question {self.current_question + 1}: {question['prompt']}")

            for i, option in enumerate(question['options']):
                if i < len(self.answer_buttons):
                    self.answer_buttons[i].setText(option)
                    self.answer_buttons[i].setVisible(True)
                    self.answer_buttons[i].setChecked(False)

            # Hide unused buttons
            for i in range(len(question['options']), len(self.answer_buttons)):
                self.answer_buttons[i].setVisible(False)
        else:
            self.finish_quiz()

    def next_question(self):
        if self.current_question < len(self.quiz_questions):
            question = self.quiz_questions[self.current_question]
            selected = self.answer_group.checkedId()

            if selected >= 0:
                selected_answer = question['options'][selected]
                if selected_answer == question['answer']:
                    self.score += 1
                    add_xp(10)  # Award XP for correct answer
                    play_correct_sound()  # Play success sound

                    # Create success particle effect
                    if hasattr(self.parent(), 'particle_system') and self.parent().particle_system:
                        button_pos = self.next_btn.mapTo(self.parent(), self.next_btn.rect().center())
                        self.parent().particle_system.create_success_explosion(button_pos, intensity=0.6)
                else:
                    play_incorrect_sound()  # Play try again sound

            self.current_question += 1
            self.score_label.setText(f"Score: {self.score}/{self.current_question}")
            self.show_question()

    def finish_quiz(self):
        self.question_label.setText(f"🎉 Quiz Complete!\nFinal Score: {self.score}/{len(self.quiz_questions)}")
        self.next_btn.setVisible(False)
        self.start_btn.setVisible(True)
        self.start_btn.setText("🔄 Take Another Quiz")

        # Hide answer buttons
        for btn in self.answer_buttons:
            btn.setVisible(False)

class TestPage(QWidget):
    def __init__(self):
        super().__init__()
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)

        title = QLabel("🧪 Tests")
        title.setFont(QFont('Segoe UI', 24, QFont.Weight.Bold))
        title.setProperty("class", "header")

        content = QLabel("Tests will be available after completing lessons!\n\nTests are more challenging than quizzes and cover multiple topics.")
        content.setFont(QFont('Segoe UI', 16))
        content.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content.setWordWrap(True)

        layout.addWidget(title)
        layout.addWidget(content)
        layout.addStretch()
        self.setLayout(layout)

class HomeworkPage(QWidget):
    def __init__(self):
        super().__init__()
        from app.homework.homework_manager import homework_manager
        self.homework_manager = homework_manager
        self.current_assignment = None
        self.current_problem_index = 0
        self.user_answers = []
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Header
        header_layout = QHBoxLayout()
        title = QLabel("📝 Daily Homework")
        title.setFont(QFont('Segoe UI', 24, QFont.Weight.Bold))
        title.setProperty("class", "header")

        # Streak info
        self.streak_label = QLabel()
        self.streak_label.setFont(QFont('Segoe UI', 16))
        self.streak_label.setProperty("class", "secondary")

        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(self.streak_label)

        # Assignment area
        self.assignment_frame = QFrame()
        self.assignment_frame.setProperty("class", "card")
        self.assignment_layout = QVBoxLayout()

        # Assignment info
        self.assignment_title = QLabel()
        self.assignment_title.setFont(QFont('Segoe UI', 18, QFont.Weight.Bold))

        self.progress_label = QLabel()
        self.progress_label.setFont(QFont('Segoe UI', 14))

        # Problem area
        self.problem_label = QLabel()
        self.problem_label.setFont(QFont('Segoe UI', 16))
        self.problem_label.setWordWrap(True)

        # Answer area
        self.answer_widget = QWidget()
        self.answer_layout = QVBoxLayout()
        self.answer_widget.setLayout(self.answer_layout)

        # Buttons with premium styling
        button_layout = QHBoxLayout()
        self.start_btn = PremiumButton("🚀 Start Today's Assignment", "", "primary")
        self.start_btn.clicked.connect(self.start_assignment)

        self.next_btn = PremiumButton("➡️ Next Problem", "", "secondary")
        self.next_btn.clicked.connect(self.next_problem)
        self.next_btn.setVisible(False)

        self.submit_btn = PremiumButton("✅ Submit Assignment", "", "success")
        self.submit_btn.clicked.connect(self.submit_assignment)
        self.submit_btn.setVisible(False)

        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.next_btn)
        button_layout.addWidget(self.submit_btn)

        # Add to assignment layout
        self.assignment_layout.addWidget(self.assignment_title)
        self.assignment_layout.addWidget(self.progress_label)
        self.assignment_layout.addWidget(self.problem_label)
        self.assignment_layout.addWidget(self.answer_widget)
        self.assignment_layout.addLayout(button_layout)
        self.assignment_frame.setLayout(self.assignment_layout)

        # Main layout
        layout.addLayout(header_layout)
        layout.addWidget(self.assignment_frame)
        layout.addStretch()
        self.setLayout(layout)

        # Load initial data
        self.update_streak_display()
        self.load_assignment()

    def update_streak_display(self):
        streak = self.homework_manager.get_streak()
        completed = self.homework_manager.get_completed_count()
        self.streak_label.setText(f"🔥 {streak} day streak | 📚 {completed} completed")

    def load_assignment(self):
        self.current_assignment = self.homework_manager.get_current_assignment()
        if self.current_assignment:
            self.assignment_title.setText(self.current_assignment["title"])
            if self.current_assignment["completed"]:
                self.show_completed_assignment()
            else:
                self.show_assignment_preview()

    def show_assignment_preview(self):
        problem_count = len(self.current_assignment["problems"])
        due_info = ""
        if self.current_assignment.get("due_date"):
            due_info = f"\nDue: {self.current_assignment['due_date'][:16]}"

        self.problem_label.setText(f"📋 Today's assignment has {problem_count} problems.{due_info}\n\nClick 'Start' when you're ready!")
        self.start_btn.setVisible(True)
        self.next_btn.setVisible(False)
        self.submit_btn.setVisible(False)

    def show_completed_assignment(self):
        score = self.current_assignment["score"]
        max_score = self.current_assignment["max_score"]
        percentage = (score / max_score * 100) if max_score > 0 else 0

        self.problem_label.setText(f"✅ Assignment completed!\nScore: {score}/{max_score} ({percentage:.0f}%)")
        self.start_btn.setText("🔄 Generate New Assignment")
        self.start_btn.setVisible(True)
        self.next_btn.setVisible(False)
        self.submit_btn.setVisible(False)

    def start_assignment(self):
        if self.current_assignment["completed"]:
            # Generate new assignment for tomorrow or reset today's
            self.homework_manager.generate_daily_assignment()
            self.load_assignment()
            return

        self.current_problem_index = 0
        self.user_answers = []
        self.start_btn.setVisible(False)
        self.show_current_problem()

    def show_current_problem(self):
        if self.current_problem_index < len(self.current_assignment["problems"]):
            problem = self.current_assignment["problems"][self.current_problem_index]

            # Update progress
            total = len(self.current_assignment["problems"])
            current = self.current_problem_index + 1
            self.progress_label.setText(f"Problem {current} of {total}")

            # Show problem
            self.problem_label.setText(f"Topic: {problem['topic']}\n\n{problem['question']}")

            # Clear previous answer widgets
            for i in reversed(range(self.answer_layout.count())):
                self.answer_layout.itemAt(i).widget().setParent(None)

            # Create answer input based on problem type
            if problem.get("type") == "multiple_choice":
                self.create_multiple_choice_input(problem["options"])
            else:
                self.create_text_input()

            # Show appropriate buttons
            if self.current_problem_index == len(self.current_assignment["problems"]) - 1:
                self.next_btn.setVisible(False)
                self.submit_btn.setVisible(True)
            else:
                self.next_btn.setVisible(True)
                self.submit_btn.setVisible(False)

    def create_multiple_choice_input(self, options):
        self.answer_group = QButtonGroup()
        for i, option in enumerate(options):
            radio = QRadioButton(option)
            radio.setFont(QFont('Segoe UI', 14))
            self.answer_group.addButton(radio, i)
            self.answer_layout.addWidget(radio)

    def create_text_input(self):
        self.answer_input = QLineEdit()
        self.answer_input.setFont(QFont('Segoe UI', 14))
        self.answer_input.setPlaceholderText("Enter your answer here...")
        self.answer_layout.addWidget(self.answer_input)

    def next_problem(self):
        # Save current answer
        self.save_current_answer()

        # Move to next problem
        self.current_problem_index += 1
        self.show_current_problem()

    def save_current_answer(self):
        problem = self.current_assignment["problems"][self.current_problem_index]

        if problem.get("type") == "multiple_choice":
            selected = self.answer_group.checkedId()
            if selected >= 0:
                answer = problem["options"][selected]
            else:
                answer = ""
        else:
            answer = self.answer_input.text().strip()

        self.user_answers.append(answer)

    def submit_assignment(self):
        # Save final answer
        self.save_current_answer()

        # Calculate score
        score = 0
        for i, problem in enumerate(self.current_assignment["problems"]):
            if i < len(self.user_answers):
                user_answer = self.user_answers[i]
                correct_answer = problem["answer"]
                if user_answer.lower() == correct_answer.lower():
                    score += 1

        # Complete assignment
        self.homework_manager.complete_assignment(self.current_assignment["id"], score)

        # Award XP
        xp_earned = score * 15  # 15 XP per correct answer
        add_xp(xp_earned)

        # Show results
        max_score = len(self.current_assignment["problems"])
        percentage = (score / max_score * 100) if max_score > 0 else 0

        self.problem_label.setText(f"🎉 Assignment submitted!\n\nScore: {score}/{max_score} ({percentage:.0f}%)\nXP Earned: {xp_earned}")

        # Update display
        self.update_streak_display()
        self.next_btn.setVisible(False)
        self.submit_btn.setVisible(False)
        self.start_btn.setText("🔄 View Results")
        self.start_btn.setVisible(True)

        # Clear answer area
        for i in reversed(range(self.answer_layout.count())):
            self.answer_layout.itemAt(i).widget().setParent(None)

class ProgressPage(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Header
        title = QLabel("📊 Your Progress")
        title.setFont(QFont('Segoe UI', 24, QFont.Weight.Bold))
        title.setProperty("class", "header")

        # Progress cards
        cards_layout = QGridLayout()

        # XP Card
        xp_card = self.create_progress_card("⭐ Experience Points", "0 XP", "Level 1")
        cards_layout.addWidget(xp_card, 0, 0)

        # Lessons Card
        lessons_card = self.create_progress_card("📖 Lessons", "0/10", "Completed")
        cards_layout.addWidget(lessons_card, 0, 1)

        # Streak Card
        streak_card = self.create_progress_card("🔥 Streak", "0 days", "Keep it up!")
        cards_layout.addWidget(streak_card, 1, 0)

        # Achievements Card
        achievements_card = self.create_progress_card("🏆 Achievements", "0", "Unlocked")
        cards_layout.addWidget(achievements_card, 1, 1)

        layout.addWidget(title)
        layout.addLayout(cards_layout)
        layout.addStretch()
        self.setLayout(layout)

        # Update with actual data
        self.update_progress()

    def create_progress_card(self, title, value, subtitle):
        card = QFrame()
        card.setProperty("class", "card")
        card.setMinimumHeight(120)

        layout = QVBoxLayout()

        title_label = QLabel(title)
        title_label.setFont(QFont('Segoe UI', 14, QFont.Weight.Bold))

        value_label = QLabel(value)
        value_label.setFont(QFont('Segoe UI', 20, QFont.Weight.Bold))
        value_label.setProperty("class", "header")

        subtitle_label = QLabel(subtitle)
        subtitle_label.setProperty("class", "secondary")

        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)
        layout.addStretch()

        card.setLayout(layout)
        return card

    def update_progress(self):
        """Update progress cards with actual data"""
        stats = get_statistics()
        level_info = get_level_progress()

        # Find and update each card
        for i in range(self.layout().itemAt(1).layout().count()):
            item = self.layout().itemAt(1).layout().itemAt(i)
            if item and item.widget():
                card = item.widget()
                card_layout = card.layout()
                if card_layout and card_layout.count() >= 3:
                    title_label = card_layout.itemAt(0).widget()
                    value_label = card_layout.itemAt(1).widget()
                    subtitle_label = card_layout.itemAt(2).widget()

                    title_text = title_label.text()

                    if "Experience Points" in title_text:
                        value_label.setText(f"{stats['total_xp']} XP")
                        subtitle_label.setText(f"Level {stats['current_level']}")
                    elif "Lessons" in title_text:
                        value_label.setText(f"{stats['lessons_completed']}/10")
                        percentage = (stats['lessons_completed'] / 10) * 100
                        subtitle_label.setText(f"{percentage:.0f}% Complete")
                    elif "Streak" in title_text:
                        value_label.setText(f"{stats['practice_streak']} days")
                        if stats['practice_streak'] > 0:
                            subtitle_label.setText("Great job!")
                        else:
                            subtitle_label.setText("Start your streak!")
                    elif "Achievements" in title_text:
                        value_label.setText(f"{stats['achievements_unlocked']}")
                        subtitle_label.setText(f"of {stats['total_achievements']}")

        # Check for new achievements
        new_achievements = check_achievements()
        if new_achievements:
            # Show achievement notification (could be enhanced with a popup)
            pass
