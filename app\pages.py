# This file wires up all the modules and pages for the main app
from PyQt5.QtWidgets import <PERSON>Widget, QVBoxLayout, QLabel, QListWidget, QTextEdit, QPushButton, QStackedWidget, QHBoxLayout
from PyQt5.QtCore import Qt
from app.lessons.topics import LESSONS
from app.cheatsheets.cheats import CHEAT_SHEETS
from app.quizzes.questions import QUESTIONS
from app.homework.assignments import HOMEWORK
from app.progress.progress import PROGRESS
from app.utils import CountdownTimer

class LessonsPage(QWidget):
    def __init__(self):
        super().__init__()
        layout = QHBoxLayout()
        self.list = QListWidget()
        for lesson in LESSONS:
            self.list.addItem(lesson["title"])
        self.text = QTextEdit()
        self.text.setReadOnly(True)
        self.list.currentRowChanged.connect(self.show_lesson)
        layout.addWidget(self.list, 1)
        layout.addWidget(self.text, 3)
        self.setLayout(layout)
        if LESSONS:
            self.list.setCurrentRow(0)

    def show_lesson(self, idx):
        if 0 <= idx < len(LESSONS):
            self.text.setText(LESSONS[idx]["content"])

class CheatSheetsPage(QWidget):
    def __init__(self):
        super().__init__()
        layout = QHBoxLayout()
        self.list = QListWidget()
        for cheat in CHEAT_SHEETS:
            self.list.addItem(cheat["title"])
        self.text = QTextEdit()
        self.text.setReadOnly(True)
        self.list.currentRowChanged.connect(self.show_cheat)
        layout.addWidget(self.list, 1)
        layout.addWidget(self.text, 3)
        self.setLayout(layout)
        if CHEAT_SHEETS:
            self.list.setCurrentRow(0)

    def show_cheat(self, idx):
        if 0 <= idx < len(CHEAT_SHEETS):
            self.text.setText(CHEAT_SHEETS[idx]["content"])

# More pages (Practice, Quizzes, Tests, Homework, Progress) would be implemented similarly
