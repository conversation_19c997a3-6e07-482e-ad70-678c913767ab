/* Premium UI Components for Math Re-teach App */

/* Cards */
.card {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-slow);
}

.card:hover::before {
  left: 100%;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-gradient {
  background: linear-gradient(135deg, var(--card-bg) 0%, var(--secondary-bg) 100%);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  user-select: none;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn:active::before {
  width: 300px;
  height: 300px;
}

.btn-primary {
  background: linear-gradient(45deg, var(--accent-color), var(--accent-hover));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-success {
  background: linear-gradient(45deg, var(--success-color), #45a049);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-warning {
  background: linear-gradient(45deg, var(--warning-color), #f57c00);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--button-bg);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--button-hover);
  border-color: var(--accent-color);
  transform: translateY(-2px);
}

.btn-ghost {
  background: transparent;
  color: var(--accent-color);
  border: 2px solid var(--accent-color);
}

.btn-ghost:hover {
  background: var(--accent-color);
  color: white;
}

.btn-lg {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: 1.2rem;
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.9rem;
}

.btn-icon {
  width: 48px;
  height: 48px;
  padding: 0;
  border-radius: var(--radius-round);
}

/* Progress Bars */
.progress {
  width: 100%;
  height: 12px;
  background: var(--secondary-bg);
  border-radius: var(--radius-md);
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-color), var(--success-color));
  border-radius: var(--radius-md);
  transition: width var(--transition-slow);
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

.progress-animated .progress-bar {
  animation: pulse 2s infinite;
}

/* Notifications */
.notification-container {
  position: fixed;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  z-index: var(--z-notification);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-width: 400px;
}

.notification {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--accent-color);
  animation: slideInRight 0.5s ease;
  position: relative;
  overflow: hidden;
}

.notification.success {
  border-left-color: var(--success-color);
}

.notification.warning {
  border-left-color: var(--warning-color);
}

.notification.error {
  border-left-color: var(--error-color);
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.notification-title {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.notification-close:hover {
  background: var(--button-hover);
  color: var(--text-primary);
}

.notification-body {
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Achievement Popup */
.achievement-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background: linear-gradient(135deg, var(--warning-color), #f57c00);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xxl);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-modal);
  text-align: center;
  color: white;
  min-width: 400px;
  animation: achievementPop 0.8s ease forwards;
}

.achievement-popup.show {
  transform: translate(-50%, -50%) scale(1);
}

.achievement-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
  animation: bounce 1s ease;
}

.achievement-text h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
}

.achievement-text p {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.9;
}

.achievement-xp {
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.5rem;
  font-weight: 700;
  display: inline-block;
}

/* Level Up Popup */
.levelup-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background: linear-gradient(135deg, var(--success-color), #45a049);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xxl);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-modal);
  text-align: center;
  color: white;
  min-width: 500px;
  animation: levelUpGlow 2s ease;
}

.levelup-popup.show {
  transform: translate(-50%, -50%) scale(1);
  animation: scaleIn 0.8s ease forwards, levelUpGlow 2s ease;
}

.levelup-icon {
  font-size: 5rem;
  margin-bottom: var(--spacing-lg);
  animation: rotate 2s ease;
}

.levelup-text h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.levelup-text p {
  font-size: 1.5rem;
  opacity: 0.9;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform var(--transition-normal);
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--button-hover);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-xl);
  max-height: 70vh;
  overflow-y: auto;
}

/* Form Elements */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--input-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.2);
}

.form-select {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--input-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.form-select:focus {
  outline: none;
  border-color: var(--accent-color);
}

/* Grid Layouts */
.grid {
  display: grid;
  gap: var(--spacing-lg);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-accent { color: var(--accent-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.bg-primary { background: var(--primary-bg); }
.bg-secondary { background: var(--secondary-bg); }
.bg-accent { background: var(--accent-color); }
.bg-success { background: var(--success-color); }
.bg-warning { background: var(--warning-color); }
.bg-error { background: var(--error-color); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-round); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* Lesson Cards */
.lessons-grid {
    margin-top: var(--spacing-xl);
}

.lesson-card {
    position: relative;
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.lesson-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.lesson-card.completed {
    border: 2px solid var(--success-color);
}

.lesson-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    display: block;
}

.lesson-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.lesson-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.5;
}

.lesson-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    font-size: 0.9rem;
}

.lesson-difficulty,
.lesson-time {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.lesson-actions {
    margin-top: var(--spacing-lg);
}

.lesson-completed-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    width: 32px;
    height: 32px;
    background: var(--success-color);
    border-radius: var(--radius-round);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    animation: scaleIn 0.5s ease;
}

/* Lesson Modal */
.lesson-content-area {
    text-align: center;
}

.lesson-icon-large {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
}

.lesson-intro h3 {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    color: var(--accent-color);
}

.lesson-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
}

.lesson-stats .stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
}

.lesson-objectives {
    text-align: left;
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-lg);
    background: var(--secondary-bg);
    border-radius: var(--radius-md);
}

.lesson-objectives h4 {
    margin-bottom: var(--spacing-md);
    color: var(--accent-color);
}

.lesson-objectives ul {
    list-style: none;
    padding: 0;
}

.lesson-objectives li {
    padding: var(--spacing-sm) 0;
    position: relative;
    padding-left: var(--spacing-lg);
}

.lesson-objectives li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.lesson-actions-modal {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-xl);
}
