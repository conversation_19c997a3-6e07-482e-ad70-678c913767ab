// qcombobox.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QComboBox : public QWidget
{
%TypeHeaderCode
#include <qcombobox.h>
%End

public:
    explicit QComboBox(QWidget *parent /TransferThis/ = 0);
    virtual ~QComboBox();
    int maxVisibleItems() const;
    void setMaxVisibleItems(int maxItems);
    int count() const /__len__/;
    void setMaxCount(int max);
    int maxCount() const;
    bool duplicatesEnabled() const;
    void setDuplicatesEnabled(bool enable);
    void setFrame(bool);
    bool hasFrame() const;
    int findText(const QString &text, Qt::MatchFlags flags = Qt::MatchExactly|Qt::MatchCaseSensitive) const;
    int findData(const QVariant &data, int role = Qt::UserRole, Qt::MatchFlags flags = Qt::MatchExactly|Qt::MatchCaseSensitive) const;

    enum InsertPolicy
    {
        NoInsert,
        InsertAtTop,
        InsertAtCurrent,
        InsertAtBottom,
        InsertAfterCurrent,
        InsertBeforeCurrent,
        InsertAlphabetically,
    };

    QComboBox::InsertPolicy insertPolicy() const;
    void setInsertPolicy(QComboBox::InsertPolicy policy);

    enum SizeAdjustPolicy
    {
        AdjustToContents,
        AdjustToContentsOnFirstShow,
        AdjustToMinimumContentsLengthWithIcon,
    };

    QComboBox::SizeAdjustPolicy sizeAdjustPolicy() const;
    void setSizeAdjustPolicy(QComboBox::SizeAdjustPolicy policy);
    int minimumContentsLength() const;
    void setMinimumContentsLength(int characters);
    QSize iconSize() const;
    void setIconSize(const QSize &size);
    bool isEditable() const;
    void setEditable(bool editable);
    void setLineEdit(QLineEdit *edit /Transfer/);
    QLineEdit *lineEdit() const;
    void setValidator(const QValidator *v /KeepReference/);
    const QValidator *validator() const;
    QAbstractItemDelegate *itemDelegate() const;
    void setItemDelegate(QAbstractItemDelegate *delegate /KeepReference/);
    QAbstractItemModel *model() const;
    virtual void setModel(QAbstractItemModel *model /KeepReference/);
    QModelIndex rootModelIndex() const;
    void setRootModelIndex(const QModelIndex &index);
    int modelColumn() const;
    void setModelColumn(int visibleColumn);
    int currentIndex() const;
    void setCurrentIndex(int index);
    QString currentText() const;
    QString itemText(int index) const;
    QIcon itemIcon(int index) const;
    QVariant itemData(int index, int role = Qt::UserRole) const;
    void addItems(const QStringList &texts);
    void addItem(const QString &text, const QVariant &userData = QVariant());
    void addItem(const QIcon &icon, const QString &text, const QVariant &userData = QVariant());
    void insertItem(int index, const QString &text, const QVariant &userData = QVariant());
    void insertItem(int index, const QIcon &icon, const QString &text, const QVariant &userData = QVariant());
    void insertItems(int index, const QStringList &texts);
    void removeItem(int index);
    void setItemText(int index, const QString &text);
    void setItemIcon(int index, const QIcon &icon);
    void setItemData(int index, const QVariant &value, int role = Qt::UserRole);
    QAbstractItemView *view() const;
    void setView(QAbstractItemView *itemView /Transfer/);
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    virtual void showPopup();
    virtual void hidePopup();
    virtual bool event(QEvent *event);
    void setCompleter(QCompleter *c /KeepReference/);
    QCompleter *completer() const;
    void insertSeparator(int index);

public slots:
    void clear();
    void clearEditText();
    void setEditText(const QString &text);
    void setCurrentText(const QString &text);

signals:
    void editTextChanged(const QString &);
    void activated(int index);
    void currentIndexChanged(int index);
    void currentTextChanged(const QString &);
    void highlighted(int index);

protected:
    virtual void initStyleOption(QStyleOptionComboBox *option) const;
    virtual void focusInEvent(QFocusEvent *e);
    virtual void focusOutEvent(QFocusEvent *e);
    virtual void changeEvent(QEvent *e);
    virtual void resizeEvent(QResizeEvent *e);
    virtual void paintEvent(QPaintEvent *e);
    virtual void showEvent(QShowEvent *e);
    virtual void hideEvent(QHideEvent *e);
    virtual void mousePressEvent(QMouseEvent *e);
    virtual void mouseReleaseEvent(QMouseEvent *e);
    virtual void keyPressEvent(QKeyEvent *e);
    virtual void keyReleaseEvent(QKeyEvent *e);
    virtual void wheelEvent(QWheelEvent *e);
    virtual void contextMenuEvent(QContextMenuEvent *e);
    virtual void inputMethodEvent(QInputMethodEvent *);

public:
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery) const;
    QVariant currentData(int role = Qt::UserRole) const;
    QVariant inputMethodQuery(Qt::InputMethodQuery query, const QVariant &argument) const;

signals:
    void textActivated(const QString &);
    void textHighlighted(const QString &);

public:
    void setPlaceholderText(const QString &placeholderText);
    QString placeholderText() const;
%If (Qt_6_9_0 -)

    enum class LabelDrawingMode
    {
        UseStyle,
        UseDelegate,
    };

%End
%If (Qt_6_9_0 -)
    QComboBox::LabelDrawingMode labelDrawingMode() const;
%End
%If (Qt_6_9_0 -)
    void setLabelDrawingMode(QComboBox::LabelDrawingMode labelDrawing);
%End
};
