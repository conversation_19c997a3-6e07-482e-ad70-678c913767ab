// qsize.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qsize.h>
%End

class QSize
{
%TypeHeaderCode
#include <qsize.h>
%End

%PickleCode
    sipRes = Py_BuildValue("ii", sipCpp->width(), sipCpp->height());
%End

public:
    void transpose();
    void scale(const QSize &s, Qt::AspectRatioMode mode);
    QSize();
    QSize(int w, int h);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QSize()");
        }
        else
        {
            sipRes = PyUnicode_FromFormat(
                    "PyQt6.QtCore.QSize(%i, %i)", sipCpp->width(), sipCpp->height());
        }
%End

    bool isNull() const;
    bool isEmpty() const;
    bool isValid() const;
    int __bool__() const;
%MethodCode
        sipRes = sipCpp->isValid();
%End

    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    int width() const;
    int height() const;
    void setWidth(int w);
    void setHeight(int h);
    void scale(int w, int h, Qt::AspectRatioMode mode);
    QSize &operator+=(const QSize &s);
    QSize &operator-=(const QSize &s);
    QSize &operator*=(qreal c);
    QSize &operator/=(qreal c);
    QSize expandedTo(const QSize &otherSize) const;
    QSize boundedTo(const QSize &otherSize) const;
    QSize scaled(const QSize &s, Qt::AspectRatioMode mode) const;
    QSize scaled(int w, int h, Qt::AspectRatioMode mode) const;
    QSize transposed() const;
    QSize grownBy(QMargins m) const;
    QSize shrunkBy(QMargins m) const;
%If (Qt_6_4_0 -)
    QSizeF toSizeF() const;
%End
};

QDataStream &operator<<(QDataStream &, const QSize &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QSize & /Constrained/) /ReleaseGIL/;
%If (Qt_6_8_0 -)
bool operator==(const QSizeF &lhs, const QSize &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QSize &lhs, const QSizeF &rhs);
%End
bool operator==(const QSize &s1, const QSize &s2);
%If (Qt_6_8_0 -)
bool operator!=(const QSizeF &lhs, const QSize &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QSize &lhs, const QSizeF &rhs);
%End
bool operator!=(const QSize &s1, const QSize &s2);
QSize operator+(const QSize &s1, const QSize &s2);
QSize operator-(const QSize &s1, const QSize &s2);
QSize operator*(const QSize &s, qreal c);
QSize operator*(qreal c, const QSize &s);
QSize operator/(const QSize &s, qreal c);

class QSizeF
{
%TypeHeaderCode
#include <qsize.h>
%End

%PickleCode
    sipRes = Py_BuildValue("dd", sipCpp->width(), sipCpp->height());
%End

public:
    void transpose();
    void scale(const QSizeF &s, Qt::AspectRatioMode mode);
    QSizeF();
    QSizeF(const QSize &sz);
    QSizeF(qreal w, qreal h);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QSizeF()");
        }
        else
        {
            PyObject *w = PyFloat_FromDouble(sipCpp->width());
            PyObject *h = PyFloat_FromDouble(sipCpp->height());
        
            if (w && h)
                sipRes = PyUnicode_FromFormat(
                        "PyQt6.QtCore.QSizeF(%R, %R)", w, h);
        
            Py_XDECREF(w);
            Py_XDECREF(h);
        }
%End

    bool isNull() const;
    bool isEmpty() const;
    bool isValid() const;
    int __bool__() const;
%MethodCode
        sipRes = sipCpp->isValid();
%End

    qreal width() const;
    qreal height() const;
    void setWidth(qreal w);
    void setHeight(qreal h);
    void scale(qreal w, qreal h, Qt::AspectRatioMode mode);
    QSizeF &operator+=(const QSizeF &s);
    QSizeF &operator-=(const QSizeF &s);
    QSizeF &operator*=(qreal c);
    QSizeF &operator/=(qreal c);
    QSizeF expandedTo(const QSizeF &otherSize) const;
    QSizeF boundedTo(const QSizeF &otherSize) const;
    QSize toSize() const;
    QSizeF scaled(const QSizeF &s, Qt::AspectRatioMode mode) const;
    QSizeF scaled(qreal w, qreal h, Qt::AspectRatioMode mode) const;
    QSizeF transposed() const;
    QSizeF grownBy(QMarginsF m) const;
    QSizeF shrunkBy(QMarginsF m) const;
};

QDataStream &operator<<(QDataStream &, const QSizeF &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QSizeF & /Constrained/) /ReleaseGIL/;
bool operator==(const QSizeF &s1, const QSizeF &s2);
bool operator!=(const QSizeF &s1, const QSizeF &s2);
QSizeF operator+(const QSizeF &s1, const QSizeF &s2);
QSizeF operator-(const QSizeF &s1, const QSizeF &s2);
QSizeF operator*(const QSizeF &s, qreal c);
QSizeF operator*(qreal c, const QSizeF &s);
QSizeF operator/(const QSizeF &s, qreal c);
%If (Qt_6_8_0 -)
bool qFuzzyCompare(const QSizeF &s1, const QSizeF &s2);
%End
%If (Qt_6_8_0 -)
bool qFuzzyIsNull(const QSizeF &size);
%End
