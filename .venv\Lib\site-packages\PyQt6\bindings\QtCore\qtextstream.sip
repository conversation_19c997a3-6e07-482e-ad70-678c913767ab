// qtextstream.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qtextstream.h>
%End

class QTextStream : public QIODeviceBase
{
%TypeHeaderCode
#include <qtextstream.h>
%End

public:
    enum RealNumberNotation
    {
        SmartNotation,
        FixedNotation,
        ScientificNotation,
    };

    enum FieldAlignment
    {
        AlignLeft,
        AlignRight,
        AlignCenter,
        AlignAccountingStyle,
    };

    enum Status
    {
        Ok,
        ReadPastEnd,
        ReadCorruptData,
        WriteFailed,
    };

    enum NumberFlag /BaseType=Flag/
    {
        ShowBase,
        ForcePoint,
        ForceSign,
        UppercaseBase,
        UppercaseDigits,
    };

    typedef QFlags<QTextStream::NumberFlag> NumberFlags;
    QTextStream();
    explicit QTextStream(QIODevice *device);
    QTextStream(QByteArray *array /Constrained/, QIODeviceBase::OpenMode mode = QIODeviceBase::ReadWrite);
    virtual ~QTextStream();
    void setEncoding(QStringConverter::Encoding encoding);
    QStringConverter::Encoding encoding() const;
    void setAutoDetectUnicode(bool enabled);
    bool autoDetectUnicode() const;
    void setGenerateByteOrderMark(bool generate);
    bool generateByteOrderMark() const;
    void setLocale(const QLocale &locale);
    QLocale locale() const;
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    QTextStream::Status status() const;
    void setStatus(QTextStream::Status status);
    void resetStatus();
    bool atEnd() const;
    void reset();
    void flush() /ReleaseGIL/;
    bool seek(qint64 pos);
    qint64 pos() const;
    void skipWhiteSpace();
    QString readLine(qint64 maxLength = 0) /ReleaseGIL/;
    QString readAll() /ReleaseGIL/;
    QString read(qint64 maxlen) /ReleaseGIL/;
    void setFieldAlignment(QTextStream::FieldAlignment alignment);
    QTextStream::FieldAlignment fieldAlignment() const;
    void setPadChar(QChar ch);
    QChar padChar() const;
    void setFieldWidth(int width);
    int fieldWidth() const;
    void setNumberFlags(QTextStream::NumberFlags flags);
    QTextStream::NumberFlags numberFlags() const;
    void setIntegerBase(int base);
    int integerBase() const;
    void setRealNumberNotation(QTextStream::RealNumberNotation notation);
    QTextStream::RealNumberNotation realNumberNotation() const;
    void setRealNumberPrecision(int precision);
    int realNumberPrecision() const;
    QTextStream &operator>>(QByteArray &array /Constrained/);
    QTextStream &operator<<(QStringView s);
    QTextStream &operator<<(const QByteArray &array);
    QTextStream &operator<<(double f /Constrained/);
    QTextStream &operator<<(SIP_PYOBJECT i /TypeHint="int"/);
%MethodCode
        qlonglong val = sipLong_AsLongLong(a1);
        
        if (!PyErr_Occurred())
        {
            sipRes = &(*a0 << val);
        }
        else
        {
            // If it is positive then it might fit an unsigned long long.
        
            qulonglong uval = sipLong_AsUnsignedLongLong(a1);
        
            if (!PyErr_Occurred())
            {
                sipRes = &(*a0 << uval);
            }
            else
            {
                sipError = (PyErr_ExceptionMatches(PyExc_OverflowError)
                        ? sipErrorFail : sipErrorContinue);
            }
        }
%End

private:
    QTextStream(const QTextStream &);
};

class QTextStreamManipulator;
QTextStream &operator<<(QTextStream &s, QTextStreamManipulator m);
QTextStreamManipulator qSetFieldWidth(int width);
QTextStreamManipulator qSetPadChar(QChar ch);
QTextStreamManipulator qSetRealNumberPrecision(int precision);

namespace Qt
{
%TypeHeaderCode
#include <qtextstream.h>
%End

    QTextStream &bin(QTextStream &s);
    QTextStream &oct(QTextStream &s);
    QTextStream &dec(QTextStream &s);
    QTextStream &hex(QTextStream &s);
    QTextStream &showbase(QTextStream &s);
    QTextStream &forcesign(QTextStream &s);
    QTextStream &forcepoint(QTextStream &s);
    QTextStream &noshowbase(QTextStream &s);
    QTextStream &noforcesign(QTextStream &s);
    QTextStream &noforcepoint(QTextStream &s);
    QTextStream &uppercasebase(QTextStream &s);
    QTextStream &uppercasedigits(QTextStream &s);
    QTextStream &lowercasebase(QTextStream &s);
    QTextStream &lowercasedigits(QTextStream &s);
    QTextStream &fixed(QTextStream &s);
    QTextStream &scientific(QTextStream &s);
    QTextStream &left(QTextStream &s);
    QTextStream &right(QTextStream &s);
    QTextStream &center(QTextStream &s);
    QTextStream &endl(QTextStream &s);
    QTextStream &flush(QTextStream &s);
    QTextStream &reset(QTextStream &s);
    QTextStream &bom(QTextStream &s);
    QTextStream &ws(QTextStream &s);
};
