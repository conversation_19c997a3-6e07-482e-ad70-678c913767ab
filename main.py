# Main entry point for the 8th/9th Grade Math Re-teach App
# Run this file to start the application

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from app.main_window import MainWindow

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Math Re-teach App")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Student Learning Tools")

    # Apply global dark theme by default
    app.setStyleSheet("""
        QApplication {
            background-color: #1e1e1e;
            color: #ffffff;
        }
    """)

    window = MainWindow()
    window.show()
    sys.exit(app.exec())
