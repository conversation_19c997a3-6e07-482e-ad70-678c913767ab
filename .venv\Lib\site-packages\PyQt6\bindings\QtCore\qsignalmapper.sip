// qsignalmapper.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSignalMapper : public QObject
{
%TypeHeaderCode
#include <qsignalmapper.h>
%End

public:
    explicit QSignalMapper(QObject *parent /TransferThis/ = 0);
    virtual ~QSignalMapper();
    void setMapping(QObject *sender, int id);
    void setMapping(QObject *sender, const QString &text);
    void setMapping(QObject *sender, QObject *object);
    void removeMappings(QObject *sender);
    QObject *mapping(int id) const;
    QObject *mapping(const QString &text) const;
    QObject *mapping(QObject *object) const;

signals:
    void mappedInt(int);
    void mappedString(const QString &);
    void mappedObject(QObject *);

public slots:
    void map();
    void map(QObject *sender);
};
