# Homework Management System
import json
import os
from datetime import datetime, timedelta
import random
from app.quizzes.questions import QUESTIONS
from app.practice_generator import PRACTICE_GENERATORS, generate_practice

HOMEWORK_FILE = os.path.join(os.path.dirname(__file__), 'homework_data.json')

class HomeworkManager:
    def __init__(self):
        self.load_homework_data()

    def load_homework_data(self):
        """Load homework data from file"""
        if os.path.exists(HOMEWORK_FILE):
            try:
                with open(HOMEWORK_FILE, 'r') as f:
                    self.data = json.load(f)
            except (json.J<PERSON>NDecodeError, IOError):
                self.data = self.get_default_data()
        else:
            self.data = self.get_default_data()

    def get_default_data(self):
        """Get default homework data structure"""
        return {
            "daily_assignments": [],
            "completed_assignments": [],
            "streak": 0,
            "last_completion_date": None,
            "settings": {
                "daily_goal": 3,
                "timer_enabled": False,
                "timer_duration": 24  # hours
            }
        }

    def save_homework_data(self):
        """Save homework data to file"""
        try:
            with open(HOMEWORK_FILE, 'w') as f:
                json.dump(self.data, f, indent=2)
        except IOError:
            pass

    def generate_daily_assignment(self):
        """Generate a new daily assignment"""
        today = datetime.now().strftime("%Y-%m-%d")
        
        # Check if we already have an assignment for today
        for assignment in self.data["daily_assignments"]:
            if assignment["date"] == today:
                return assignment

        # Generate new assignment
        topics = list(PRACTICE_GENERATORS.keys())
        selected_topics = random.sample(topics, min(3, len(topics)))
        
        problems = []
        for topic in selected_topics:
            problem = generate_practice(topic)
            problems.append({
                "topic": topic,
                "question": problem["question"],
                "answer": problem["answer"],
                "hints": problem.get("hints", []),
                "steps": problem.get("steps", [])
            })

        # Add some quiz questions
        quiz_questions = random.sample(QUESTIONS, min(2, len(QUESTIONS)))
        for q in quiz_questions:
            problems.append({
                "topic": q["topic"],
                "question": q["prompt"],
                "answer": q["answer"],
                "options": q["options"],
                "type": "multiple_choice"
            })

        assignment = {
            "id": f"daily_{today}",
            "date": today,
            "title": f"Daily Practice - {today}",
            "problems": problems,
            "completed": False,
            "score": 0,
            "max_score": len(problems),
            "due_date": self.calculate_due_date() if self.data["settings"]["timer_enabled"] else None
        }

        self.data["daily_assignments"].append(assignment)
        self.save_homework_data()
        return assignment

    def calculate_due_date(self):
        """Calculate due date based on timer settings"""
        hours = self.data["settings"]["timer_duration"]
        due_date = datetime.now() + timedelta(hours=hours)
        return due_date.isoformat()

    def get_current_assignment(self):
        """Get the current day's assignment"""
        return self.generate_daily_assignment()

    def complete_assignment(self, assignment_id, score):
        """Mark an assignment as completed"""
        for assignment in self.data["daily_assignments"]:
            if assignment["id"] == assignment_id:
                assignment["completed"] = True
                assignment["score"] = score
                assignment["completion_date"] = datetime.now().isoformat()
                
                # Update streak
                self.update_streak()
                
                # Move to completed assignments
                self.data["completed_assignments"].append(assignment)
                self.data["daily_assignments"].remove(assignment)
                
                self.save_homework_data()
                return True
        return False

    def update_streak(self):
        """Update the homework completion streak"""
        today = datetime.now().date()
        last_date = self.data.get("last_completion_date")
        
        if last_date:
            last_date = datetime.fromisoformat(last_date).date()
            if today == last_date + timedelta(days=1):
                # Consecutive day
                self.data["streak"] += 1
            elif today == last_date:
                # Same day (don't change streak)
                pass
            else:
                # Streak broken
                self.data["streak"] = 1
        else:
            # First completion
            self.data["streak"] = 1
        
        self.data["last_completion_date"] = today.isoformat()

    def get_streak(self):
        """Get current homework streak"""
        return self.data["streak"]

    def get_completed_count(self):
        """Get total number of completed assignments"""
        return len(self.data["completed_assignments"])

    def is_assignment_overdue(self, assignment):
        """Check if an assignment is overdue"""
        if not assignment.get("due_date"):
            return False
        
        due_date = datetime.fromisoformat(assignment["due_date"])
        return datetime.now() > due_date

    def get_settings(self):
        """Get homework settings"""
        return self.data["settings"]

    def update_settings(self, settings):
        """Update homework settings"""
        self.data["settings"].update(settings)
        self.save_homework_data()

# Global homework manager instance
homework_manager = HomeworkManager()
