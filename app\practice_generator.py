# Practice generator for 8th & 9th grade math
# Generates random problems per lesson and grade, with step-by-step and hint support
import random

def generate_linear_equation():
    m = random.randint(-10, 10)
    b = random.randint(-20, 20)
    x = random.randint(-10, 10)
    y = m * x + b
    return {
        "question": f"If y = {m}x + {b}, what is y when x = {x}?",
        "answer": str(y),
        "steps": [
            f"Plug x = {x} into the equation: y = {m}*{x} + {b}",
            f"Calculate: {m}*{x} = {m*x}",
            f"Add {b}: {m*x} + {b} = {y}",
            f"Final answer: y = {y}"
        ],
        "hints": [
            "Remember y = mx + b.",
            f"First, multiply m and x: {m}*{x}",
            f"Then add b: {m*x} + {b}"
        ]
    }

def generate_pythagorean():
    a = random.randint(3, 12)
    b = random.randint(3, 12)
    c = int((a**2 + b**2) ** 0.5)
    return {
        "question": f"What is the hypotenuse of a right triangle with legs {a} and {b}?",
        "answer": str(c),
        "steps": [
            f"Use a^2 + b^2 = c^2: {a}^2 + {b}^2 = c^2",
            f"Calculate: {a**2} + {b**2} = {a**2 + b**2}",
            f"Take the square root: sqrt({a**2 + b**2}) = {c}",
            f"Final answer: c = {c}"
        ],
        "hints": [
            "Use the Pythagorean theorem.",
            f"Add the squares: {a**2} + {b**2}",
            f"Take the square root."
        ]
    }

# Add more generators for each lesson as needed

PRACTICE_GENERATORS = {
    "Linear Equations": generate_linear_equation,
    "Pythagorean Theorem": generate_pythagorean,
    # ...add more as you build out lessons...
}

def generate_practice(lesson_name):
    if lesson_name in PRACTICE_GENERATORS:
        return PRACTICE_GENERATORS[lesson_name]()
    return {"question": "Practice not available yet.", "answer": "", "steps": [], "hints": []}
