# Practice generator for 8th & 9th grade math
# Generates random problems per lesson and grade, with step-by-step and hint support
import random

def generate_linear_equation():
    m = random.randint(-10, 10)
    b = random.randint(-20, 20)
    x = random.randint(-10, 10)
    y = m * x + b
    return {
        "question": f"If y = {m}x + {b}, what is y when x = {x}?",
        "answer": str(y),
        "steps": [
            f"Plug x = {x} into the equation: y = {m}*{x} + {b}",
            f"Calculate: {m}*{x} = {m*x}",
            f"Add {b}: {m*x} + {b} = {y}",
            f"Final answer: y = {y}"
        ],
        "hints": [
            "Remember y = mx + b.",
            f"First, multiply m and x: {m}*{x}",
            f"Then add b: {m*x} + {b}"
        ]
    }

def generate_pythagorean():
    a = random.randint(3, 12)
    b = random.randint(3, 12)
    c = int((a**2 + b**2) ** 0.5)
    return {
        "question": f"What is the hypotenuse of a right triangle with legs {a} and {b}?",
        "answer": str(c),
        "steps": [
            f"Use a^2 + b^2 = c^2: {a}^2 + {b}^2 = c^2",
            f"Calculate: {a**2} + {b**2} = {a**2 + b**2}",
            f"Take the square root: sqrt({a**2 + b**2}) = {c}",
            f"Final answer: c = {c}"
        ],
        "hints": [
            "Use the Pythagorean theorem.",
            f"Add the squares: {a**2} + {b**2}",
            f"Take the square root."
        ]
    }

def generate_quadratic():
    a = random.randint(1, 5)
    b = random.randint(-10, 10)
    c = random.randint(-10, 10)
    return {
        "question": f"What is the discriminant of {a}x² + {b}x + {c} = 0?",
        "answer": str(b*b - 4*a*c),
        "steps": [
            f"Use the discriminant formula: b² - 4ac",
            f"Substitute values: ({b})² - 4({a})({c})",
            f"Calculate: {b*b} - {4*a*c}",
            f"Final answer: {b*b - 4*a*c}"
        ],
        "hints": [
            "The discriminant formula is b² - 4ac",
            f"Here, a={a}, b={b}, c={c}",
            f"Calculate step by step"
        ]
    }

def generate_factoring():
    # Generate a simple quadratic that factors nicely
    p = random.randint(1, 6)
    q = random.randint(1, 6)
    # (x + p)(x + q) = x² + (p+q)x + pq
    b = p + q
    c = p * q
    return {
        "question": f"Factor x² + {b}x + {c}",
        "answer": f"(x+{p})(x+{q})",
        "steps": [
            f"Find two numbers that multiply to {c} and add to {b}",
            f"Those numbers are {p} and {q}",
            f"So x² + {b}x + {c} = (x+{p})(x+{q})"
        ],
        "hints": [
            f"Look for two numbers that multiply to {c}",
            f"Those same numbers should add to {b}",
            f"Try {p} and {q}"
        ]
    }

def generate_systems():
    x_val = random.randint(1, 5)
    y_val = random.randint(1, 5)
    a1, b1 = random.randint(1, 3), random.randint(1, 3)
    a2, b2 = random.randint(1, 3), random.randint(1, 3)
    c1 = a1 * x_val + b1 * y_val
    c2 = a2 * x_val + b2 * y_val

    return {
        "question": f"Solve the system:\n{a1}x + {b1}y = {c1}\n{a2}x + {b2}y = {c2}",
        "answer": f"x={x_val}, y={y_val}",
        "steps": [
            "Use substitution or elimination method",
            f"From the first equation: x = ({c1} - {b1}y)/{a1}",
            f"Substitute into second equation and solve for y",
            f"Then find x using the value of y"
        ],
        "hints": [
            "Try the elimination method",
            "Multiply equations to eliminate one variable",
            f"The solution is x={x_val}, y={y_val}"
        ]
    }

def generate_exponents():
    base = random.randint(2, 5)
    exp = random.randint(2, 4)
    result = base ** exp
    return {
        "question": f"What is {base}^{exp}?",
        "answer": str(result),
        "steps": [
            f"{base}^{exp} means {base} multiplied by itself {exp} times",
            f"Calculate: {' × '.join([str(base)] * exp)}",
            f"Result: {result}"
        ],
        "hints": [
            f"Remember: {base}^{exp} = {base} × {base} × ...",
            f"Multiply {base} by itself {exp} times",
            f"The answer is {result}"
        ]
    }

# Add more generators for each lesson as needed

PRACTICE_GENERATORS = {
    "Linear Equations": generate_linear_equation,
    "Pythagorean Theorem": generate_pythagorean,
    "Quadratic Formula": generate_quadratic,
    "Factoring": generate_factoring,
    "Systems of Equations": generate_systems,
    "Exponents and Roots": generate_exponents,
}

def generate_practice(lesson_name):
    if lesson_name in PRACTICE_GENERATORS:
        return PRACTICE_GENERATORS[lesson_name]()
    return {"question": "Practice not available yet.", "answer": "", "steps": [], "hints": []}
