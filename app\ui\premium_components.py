# Premium UI Components for Math Re-teach App
from PyQt6.QtWidgets import (QWidget, QPushButton, QLabel, QFrame, QVBoxLayout, 
                            QHBoxLayout, QProgressBar, QGraphicsDropShadowEffect)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect, pyqtProperty, QPoint
from PyQt6.QtGui import (QPainter, QColor, QBrush, QPen, QFont, QLinearGradient, 
                        QRadialGradient, QPainterPath, QPixmap, QFontMetrics)
from app.ui.animations import AnimatedWidget, animation_manager
import math

class PremiumButton(AnimatedWidget):
    """Beautiful animated button with hover effects and gradients"""
    clicked = pyqtSignal()
    
    def __init__(self, text="", icon="", button_type="primary", parent=None):
        super().__init__(parent)
        self.text = text
        self.icon = icon
        self.button_type = button_type
        self.setMinimumHeight(50)
        self.setMinimumWidth(120)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # Animation properties
        self._hover_scale = 1.02
        self._click_scale = 0.98
        self.glow_intensity = 0.0
        
        # Create glow animation
        self.glow_animation = QPropertyAnimation(self, b"glow_intensity")
        self.glow_animation.setDuration(300)
        self.glow_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def get_glow_intensity(self):
        return getattr(self, '_glow_intensity', 0.0)

    def set_glow_intensity(self, value):
        self._glow_intensity = value
        self.update()

    glow_intensity = pyqtProperty(float, get_glow_intensity, set_glow_intensity)
    
    def enterEvent(self, event):
        super().enterEvent(event)
        self.glow_animation.setStartValue(0.0)
        self.glow_animation.setEndValue(1.0)
        self.glow_animation.start()
    
    def leaveEvent(self, event):
        super().leaveEvent(event)
        self.glow_animation.setStartValue(1.0)
        self.glow_animation.setEndValue(0.0)
        self.glow_animation.start()
    
    def mouseReleaseEvent(self, event):
        super().mouseReleaseEvent(event)
        if self.rect().contains(event.pos()):
            self.clicked.emit()
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect().adjusted(5, 5, -5, -5)
        
        # Create gradient based on button type
        if self.button_type == "primary":
            gradient = QLinearGradient(0, 0, 0, rect.height())
            gradient.setColorAt(0, QColor(74, 158, 255))
            gradient.setColorAt(1, QColor(45, 123, 231))
        elif self.button_type == "success":
            gradient = QLinearGradient(0, 0, 0, rect.height())
            gradient.setColorAt(0, QColor(76, 175, 80))
            gradient.setColorAt(1, QColor(56, 142, 60))
        elif self.button_type == "warning":
            gradient = QLinearGradient(0, 0, 0, rect.height())
            gradient.setColorAt(0, QColor(255, 152, 0))
            gradient.setColorAt(1, QColor(245, 124, 0))
        else:  # secondary
            gradient = QLinearGradient(0, 0, 0, rect.height())
            gradient.setColorAt(0, QColor(108, 117, 125))
            gradient.setColorAt(1, QColor(73, 80, 87))
        
        # Draw shadow/glow
        if hasattr(self, '_glow_intensity') and self._glow_intensity > 0:
            glow_rect = rect.adjusted(-3, -3, 3, 3)
            glow_color = QColor(74, 158, 255, int(50 * self._glow_intensity))
            painter.setBrush(QBrush(glow_color))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawRoundedRect(glow_rect, 12, 12)
        
        # Draw button background
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(rect, 8, 8)
        
        # Draw text
        painter.setPen(QColor(255, 255, 255))
        font = QFont("Segoe UI", 12, QFont.Weight.Medium)
        painter.setFont(font)
        
        text_rect = rect
        if self.icon:
            text_to_draw = f"{self.icon} {self.text}"
        else:
            text_to_draw = self.text
            
        painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter, text_to_draw)

class GlassCard(QFrame):
    """Beautiful glass-morphism card with blur effect"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(100)
        self.opacity = 0.9
        
        # Add subtle shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect().adjusted(2, 2, -2, -2)
        
        # Create glass effect
        gradient = QLinearGradient(0, 0, 0, rect.height())
        gradient.setColorAt(0, QColor(255, 255, 255, int(40 * self.opacity)))
        gradient.setColorAt(1, QColor(255, 255, 255, int(10 * self.opacity)))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(QColor(255, 255, 255, 60), 1))
        painter.drawRoundedRect(rect, 16, 16)

class AnimatedProgressBar(QWidget):
    """Beautiful animated progress bar with gradient and glow"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(20)
        self.value = 0
        self.maximum = 100
        self.animated_value = 0
        
        # Animation for progress changes
        self.progress_animation = QPropertyAnimation(self, b"animated_value")
        self.progress_animation.setDuration(800)
        self.progress_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
    def get_animated_value(self):
        return getattr(self, '_animated_value', 0.0)

    def set_animated_value(self, value):
        self._animated_value = value
        self.update()

    animated_value = pyqtProperty(float, get_animated_value, set_animated_value)
    
    def setValue(self, value):
        self.value = max(0, min(value, self.maximum))
        self.progress_animation.setStartValue(getattr(self, '_animated_value', 0))
        self.progress_animation.setEndValue(self.value)
        self.progress_animation.start()
    
    def setMaximum(self, maximum):
        self.maximum = maximum
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect().adjusted(2, 2, -2, -2)
        
        # Draw background
        painter.setBrush(QBrush(QColor(240, 240, 240, 100)))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(rect, 10, 10)
        
        # Draw progress
        if hasattr(self, '_animated_value') and self._animated_value > 0:
            progress_width = int((self._animated_value / self.maximum) * rect.width())
            progress_rect = QRect(rect.x(), rect.y(), progress_width, rect.height())
            
            # Create gradient
            gradient = QLinearGradient(0, 0, progress_width, 0)
            gradient.setColorAt(0, QColor(74, 158, 255))
            gradient.setColorAt(0.5, QColor(45, 123, 231))
            gradient.setColorAt(1, QColor(74, 158, 255))
            
            painter.setBrush(QBrush(gradient))
            painter.drawRoundedRect(progress_rect, 10, 10)
            
            # Add glow effect
            glow_rect = progress_rect.adjusted(-2, -2, 2, 2)
            glow_color = QColor(74, 158, 255, 30)
            painter.setBrush(QBrush(glow_color))
            painter.drawRoundedRect(glow_rect, 12, 12)

class FloatingNotification(AnimatedWidget):
    """Floating notification with smooth animations"""
    
    def __init__(self, text="", notification_type="info", parent=None):
        super().__init__(parent)
        self.text = text
        self.notification_type = notification_type
        self.setFixedHeight(60)
        self.setMinimumWidth(300)
        
        # Auto-hide timer
        self.hide_timer = QTimer()
        self.hide_timer.setSingleShot(True)
        self.hide_timer.timeout.connect(self.hide_notification)
        
        # Position at top of parent
        if parent:
            self.move(parent.width() // 2 - 150, -70)
    
    def show_notification(self, duration=3000):
        """Show notification with slide-down animation"""
        self.show()
        
        # Slide down animation
        slide_anim = animation_manager.create_slide_animation(
            self, duration=400, 
            start_pos=self.pos(),
            end_pos=QPoint(self.x(), 20),
            direction="down"
        )
        
        # Fade in animation
        fade_anim = animation_manager.create_fade_animation(
            self, duration=400, start_opacity=0.0, end_opacity=1.0
        )
        
        slide_anim.start()
        fade_anim.start()
        
        # Auto-hide after duration
        self.hide_timer.start(duration)
    
    def hide_notification(self):
        """Hide notification with slide-up animation"""
        # Slide up animation
        slide_anim = animation_manager.create_slide_animation(
            self, duration=400,
            start_pos=self.pos(),
            end_pos=QPoint(self.x(), -70),
            direction="up"
        )
        
        # Fade out animation
        fade_anim = animation_manager.create_fade_animation(
            self, duration=400, start_opacity=1.0, end_opacity=0.0
        )
        
        slide_anim.finished.connect(self.hide)
        slide_anim.start()
        fade_anim.start()
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect().adjusted(5, 5, -5, -5)
        
        # Choose color based on type
        if self.notification_type == "success":
            color = QColor(76, 175, 80)
        elif self.notification_type == "warning":
            color = QColor(255, 152, 0)
        elif self.notification_type == "error":
            color = QColor(244, 67, 54)
        else:  # info
            color = QColor(74, 158, 255)
        
        # Draw background with gradient
        gradient = QLinearGradient(0, 0, 0, rect.height())
        gradient.setColorAt(0, color)
        gradient.setColorAt(1, color.darker(120))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(rect, 12, 12)
        
        # Draw text
        painter.setPen(QColor(255, 255, 255))
        font = QFont("Segoe UI", 11, QFont.Weight.Medium)
        painter.setFont(font)
        painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, self.text)

class PulsingXPLabel(QLabel):
    """XP label that pulses when value changes"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_xp = 0
        self.target_xp = 0
        
        # Pulse animation
        self.pulse_animation = QPropertyAnimation(self, b"geometry")
        self.pulse_animation.setDuration(600)
        self.pulse_animation.setEasingCurve(QEasingCurve.Type.OutElastic)
        
    def setXP(self, xp):
        """Set XP with animation"""
        if xp > self.current_xp:
            # Animate the number change
            self.animate_xp_change(self.current_xp, xp)
            
            # Pulse animation
            original_rect = self.geometry()
            center = original_rect.center()
            
            # Scale up then back down
            scaled_rect = QRect(0, 0, int(original_rect.width() * 1.2), 
                              int(original_rect.height() * 1.2))
            scaled_rect.moveCenter(center)
            
            self.pulse_animation.setStartValue(original_rect)
            self.pulse_animation.setKeyValueAt(0.3, scaled_rect)
            self.pulse_animation.setEndValue(original_rect)
            self.pulse_animation.start()
        
        self.current_xp = xp
    
    def animate_xp_change(self, start_xp, end_xp):
        """Animate XP number change"""
        def update_display():
            progress = self.xp_animation.currentTime() / self.xp_animation.duration()
            current_value = start_xp + (end_xp - start_xp) * progress
            self.setText(f"⭐ {int(current_value)} XP")
        
        self.xp_animation = QPropertyAnimation()
        self.xp_animation.setDuration(800)
        self.xp_animation.valueChanged.connect(update_display)
        self.xp_animation.setStartValue(0)
        self.xp_animation.setEndValue(1)
        self.xp_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.xp_animation.start()
