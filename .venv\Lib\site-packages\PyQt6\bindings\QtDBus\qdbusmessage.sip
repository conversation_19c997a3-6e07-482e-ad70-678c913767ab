// qdbusmessage.sip generated by MetaSIP
//
// This file is part of the QtDBus Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDBusMessage
{
%TypeHeaderCode
#include <qdbusmessage.h>
%End

public:
    enum MessageType
    {
        InvalidMessage,
        MethodCallMessage,
        ReplyMessage,
        ErrorMessage,
        SignalMessage,
    };

    QDBusMessage();
    QDBusMessage(const QDBusMessage &other);
    ~QDBusMessage();
    static QDBusMessage createSignal(const QString &path, const QString &interface, const QString &name);
    static QDBusMessage createMethodCall(const QString &service, const QString &path, const QString &interface, const QString &method);
    static QDBusMessage createError(const QString &name, const QString &msg);
    static QDBusMessage createError(const QDBusError &error);
    static QDBusMessage createError(QDBusError::ErrorType type, const QString &msg);
    QDBusMessage createReply(const QList<QVariant> &arguments = QList<QVariant>()) const;
    QDBusMessage createReply(const QVariant &argument) const;
    QDBusMessage createErrorReply(const QString &name, const QString &msg) const;
    QDBusMessage createErrorReply(const QDBusError &error) const;
    QDBusMessage createErrorReply(QDBusError::ErrorType type, const QString &msg) const;
    QString service() const;
    QString path() const;
    QString interface() const;
    QString member() const;
    QString errorName() const;
    QString errorMessage() const;
    QDBusMessage::MessageType type() const;
    QString signature() const;
    bool isReplyRequired() const;
    void setDelayedReply(bool enable) const;
    bool isDelayedReply() const;
    void setAutoStartService(bool enable);
    bool autoStartService() const;
    void setArguments(const QList<QVariant> &arguments);
    QList<QVariant> arguments() const;
    QDBusMessage &operator<<(const QVariant &arg);
    void swap(QDBusMessage &other /Constrained/);
    static QDBusMessage createTargetedSignal(const QString &service, const QString &path, const QString &interface, const QString &name);
    void setInteractiveAuthorizationAllowed(bool enable);
    bool isInteractiveAuthorizationAllowed() const;
};
