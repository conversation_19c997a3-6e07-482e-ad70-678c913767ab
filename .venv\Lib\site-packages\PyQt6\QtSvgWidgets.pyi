# The PEP 484 type hints stub file for the QtSvgWidgets module.
#
# Generated by SIP 6.12.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore
from PyQt6 import QtGui
from PyQt6 import QtSvg
from PyQt6 import QtWidgets

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]


class QGraphicsSvgItem(QtWidgets.QGraphicsObject):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtWidgets.QGraphicsItem] = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: typing.Optional[str], parent: typing.Optional[QtWidgets.QGraphicsItem] = ...) -> None: ...

    def type(self) -> int: ...
    def paint(self, painter: typing.Optional[QtGui.QPainter], option: typing.Optional[QtWidgets.QStyleOptionGraphicsItem], widget: typing.Optional[QtWidgets.QWidget] = ...) -> None: ...
    def boundingRect(self) -> QtCore.QRectF: ...
    def maximumCacheSize(self) -> QtCore.QSize: ...
    def setMaximumCacheSize(self, size: QtCore.QSize) -> None: ...
    def elementId(self) -> str: ...
    def setElementId(self, id: typing.Optional[str]) -> None: ...
    def renderer(self) -> typing.Optional[QtSvg.QSvgRenderer]: ...
    def setSharedRenderer(self, renderer: typing.Optional[QtSvg.QSvgRenderer]) -> None: ...


class QSvgWidget(QtWidgets.QWidget):

    @typing.overload
    def __init__(self, parent: typing.Optional[QtWidgets.QWidget] = ...) -> None: ...
    @typing.overload
    def __init__(self, file: typing.Optional[str], parent: typing.Optional[QtWidgets.QWidget] = ...) -> None: ...

    def setOptions(self, options: QtSvg.QtSvg.Option) -> None: ...
    def options(self) -> QtSvg.QtSvg.Option: ...
    def paintEvent(self, event: typing.Optional[QtGui.QPaintEvent]) -> None: ...
    @typing.overload
    def load(self, file: typing.Optional[str]) -> None: ...
    @typing.overload
    def load(self, contents: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def sizeHint(self) -> QtCore.QSize: ...
    def renderer(self) -> typing.Optional[QtSvg.QSvgRenderer]: ...
