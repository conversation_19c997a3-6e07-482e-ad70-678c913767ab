{"name": "animate.css", "version": "4.1.1", "main": "animate.css", "repository": {"type": "git", "url": "https://github.com/animate-css/animate.css.git"}, "author": {"name": "Animate.css"}, "homepage": "https://animate.style/", "license": "MIT", "animateConfig": {"prefix": "animate__"}, "scripts": {"start": "npm-run-all raw prod compat", "compat": "npx postcss source/animate.css -o animate.compat.css --no-map --env compat", "dev": "npx postcss source/animate.css -o animate.css --no-map --env development -w", "raw": "npx postcss source/animate.css -o animate.css --no-map --env development", "prod": "npx postcss source/animate.css -o animate.min.css --no-map --env production", "format": "prettier --write \"**/*.{js,json,md,css}\"", "precommit": "lint-staged", "docs:library": "npx postcss source/animate.css -o ./docs/animate.min.css --no-map --env production", "docs:pages": "node ./docsSource/index.js", "docs": "npm-run-all docs:library docs:pages", "version": "npm-run-all start docs && git add -A docs animate.css animate.min.css animate.compat.css", "postversion": "git push && git push --tags"}, "browserslist": ["> 3%", "last 2 versions"], "style": "./animate.css", "jspm": {"main": "animate.css!", "format": "global", "directories": {"lib": "./"}}, "devDependencies": {"autoprefixer": "^9.7.6", "cssnano": "^4.1.10", "eslint": "^7.8.1", "husky": "^4.2.5", "lint-staged": "^10.3.0", "markdown-it": "^11.0.0", "npm-run-all": "^4.1.5", "postcss": "^7.0.27", "postcss-cli": "^7.1.2", "postcss-header": "^2.0.0", "postcss-import": "^12.0.1", "postcss-prefixer": "^2.1.2", "postcss-preset-env": "^6.7.0", "prettier": "^2.1.1"}, "lint-staged": {"*.{js,json,md,css}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "npm-run-all start precommit"}}, "files": ["animate.compat.css", "animate.min.css", "animate.css", "source/**/*.css"]}