# Modern Theme System for Math Re-teach App
# Supports multiple themes, dark/light modes, and unlockable themes

class Theme:
    def __init__(self, name, colors, unlocked=True):
        self.name = name
        self.colors = colors
        self.unlocked = unlocked

# Color schemes for different themes
THEMES = {
    "dark_modern": Theme("Dark Modern", {
        "primary": "#1e1e1e",
        "secondary": "#2d2d2d", 
        "accent": "#4a9eff",
        "accent_hover": "#3d8ce6",
        "text": "#ffffff",
        "text_secondary": "#b0b0b0",
        "success": "#4caf50",
        "warning": "#ff9800",
        "error": "#f44336",
        "sidebar": "#232629",
        "header": "#1a1c1e",
        "card": "#2d2d2d",
        "border": "#404040",
        "button": "#3d3d3d",
        "button_hover": "#4d4d4d",
        "input": "#2d2d2d",
        "progress": "#4a9eff"
    }),
    
    "light_modern": Theme("Light Modern", {
        "primary": "#ffffff",
        "secondary": "#f5f5f5",
        "accent": "#2196f3",
        "accent_hover": "#1976d2",
        "text": "#212121",
        "text_secondary": "#757575",
        "success": "#4caf50",
        "warning": "#ff9800", 
        "error": "#f44336",
        "sidebar": "#f0f0f0",
        "header": "#ffffff",
        "card": "#ffffff",
        "border": "#e0e0e0",
        "button": "#f5f5f5",
        "button_hover": "#e0e0e0",
        "input": "#ffffff",
        "progress": "#2196f3"
    }),
    
    "neon_dark": Theme("Neon Dark", {
        "primary": "#0a0a0a",
        "secondary": "#1a1a1a",
        "accent": "#00ff88",
        "accent_hover": "#00cc6a",
        "text": "#ffffff",
        "text_secondary": "#cccccc",
        "success": "#00ff88",
        "warning": "#ffaa00",
        "error": "#ff4444",
        "sidebar": "#111111",
        "header": "#0f0f0f",
        "card": "#1a1a1a",
        "border": "#333333",
        "button": "#222222",
        "button_hover": "#333333",
        "input": "#1a1a1a",
        "progress": "#00ff88"
    }, unlocked=False),
    
    "ocean_blue": Theme("Ocean Blue", {
        "primary": "#0d1421",
        "secondary": "#1e2a3a",
        "accent": "#00bcd4",
        "accent_hover": "#0097a7",
        "text": "#ffffff",
        "text_secondary": "#b0bec5",
        "success": "#4caf50",
        "warning": "#ff9800",
        "error": "#f44336",
        "sidebar": "#162332",
        "header": "#0f1419",
        "card": "#1e2a3a",
        "border": "#37474f",
        "button": "#263238",
        "button_hover": "#37474f",
        "input": "#1e2a3a",
        "progress": "#00bcd4"
    }, unlocked=False),
    
    "sunset_orange": Theme("Sunset Orange", {
        "primary": "#1a0f0a",
        "secondary": "#2d1b0f",
        "accent": "#ff6b35",
        "accent_hover": "#e55a2b",
        "text": "#ffffff",
        "text_secondary": "#d4af37",
        "success": "#4caf50",
        "warning": "#ff9800",
        "error": "#f44336",
        "sidebar": "#221510",
        "header": "#1a0f0a",
        "card": "#2d1b0f",
        "border": "#4a2c1a",
        "button": "#3d2415",
        "button_hover": "#4a2c1a",
        "input": "#2d1b0f",
        "progress": "#ff6b35"
    }, unlocked=False)
}

def get_theme_stylesheet(theme_name):
    """Generate complete stylesheet for a theme"""
    if theme_name not in THEMES:
        theme_name = "dark_modern"
    
    theme = THEMES[theme_name]
    colors = theme.colors
    
    return f"""
    /* Main Application Styling */
    QMainWindow {{
        background-color: {colors['primary']};
        color: {colors['text']};
    }}
    
    /* Sidebar Styling */
    QFrame#sidebar {{
        background-color: {colors['sidebar']};
        border-right: 2px solid {colors['border']};
    }}
    
    /* Header Styling */
    QFrame#header {{
        background-color: {colors['header']};
        border-bottom: 2px solid {colors['border']};
    }}
    
    /* Sidebar Buttons */
    QPushButton.sidebar-btn {{
        background-color: transparent;
        color: {colors['text']};
        text-align: left;
        padding: 12px 16px;
        border: none;
        border-radius: 8px;
        margin: 2px 8px;
        font-size: 14px;
        font-weight: 500;
    }}
    
    QPushButton.sidebar-btn:hover {{
        background-color: {colors['button_hover']};
    }}
    
    QPushButton.sidebar-btn:checked {{
        background-color: {colors['accent']};
        color: white;
    }}
    
    /* Regular Buttons */
    QPushButton {{
        background-color: {colors['button']};
        color: {colors['text']};
        border: 2px solid {colors['border']};
        border-radius: 8px;
        padding: 8px 16px;
        font-size: 14px;
        font-weight: 500;
    }}
    
    QPushButton:hover {{
        background-color: {colors['button_hover']};
        border-color: {colors['accent']};
    }}
    
    QPushButton:pressed {{
        background-color: {colors['accent']};
        color: white;
    }}
    
    /* Primary Buttons */
    QPushButton.primary {{
        background-color: {colors['accent']};
        color: white;
        border: none;
    }}
    
    QPushButton.primary:hover {{
        background-color: {colors['accent_hover']};
    }}
    
    /* Labels */
    QLabel {{
        color: {colors['text']};
    }}
    
    QLabel.secondary {{
        color: {colors['text_secondary']};
    }}
    
    QLabel.header {{
        font-size: 16px;
        font-weight: bold;
        color: {colors['text']};
    }}
    
    /* List Widgets */
    QListWidget {{
        background-color: {colors['card']};
        color: {colors['text']};
        border: 2px solid {colors['border']};
        border-radius: 8px;
        padding: 8px;
    }}
    
    QListWidget::item {{
        padding: 8px;
        border-radius: 4px;
        margin: 2px 0px;
    }}
    
    QListWidget::item:selected {{
        background-color: {colors['accent']};
        color: white;
    }}
    
    QListWidget::item:hover {{
        background-color: {colors['button_hover']};
    }}
    
    /* Text Edits */
    QTextEdit {{
        background-color: {colors['card']};
        color: {colors['text']};
        border: 2px solid {colors['border']};
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        line-height: 1.5;
    }}
    
    /* Progress Bars */
    QProgressBar {{
        background-color: {colors['secondary']};
        border: 2px solid {colors['border']};
        border-radius: 8px;
        text-align: center;
        font-weight: bold;
    }}
    
    QProgressBar::chunk {{
        background-color: {colors['progress']};
        border-radius: 6px;
    }}
    
    /* Menu Bar */
    QMenuBar {{
        background-color: {colors['header']};
        color: {colors['text']};
        border-bottom: 1px solid {colors['border']};
    }}
    
    QMenuBar::item {{
        padding: 8px 12px;
        background-color: transparent;
    }}
    
    QMenuBar::item:selected {{
        background-color: {colors['accent']};
        color: white;
    }}
    
    /* Scroll Bars */
    QScrollBar:vertical {{
        background-color: {colors['secondary']};
        width: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {colors['accent']};
        border-radius: 6px;
        min-height: 20px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {colors['accent_hover']};
    }}
    """

def get_available_themes():
    """Get list of available theme names"""
    return list(THEMES.keys())

def is_theme_unlocked(theme_name):
    """Check if a theme is unlocked"""
    if theme_name in THEMES:
        return THEMES[theme_name].unlocked
    return False

def unlock_theme(theme_name):
    """Unlock a theme"""
    if theme_name in THEMES:
        THEMES[theme_name].unlocked = True
