# Advanced Animation System for Math Re-teach App
from PyQt6.QtCore import (QPropertyAnimation, QEasingCurve, QSequentialAnimationGroup,
                         QParallelAnimationGroup, QTimer, QRect, QPoint, QSize, pyqtProperty, Qt)
from PyQt6.QtWidgets import QWidget, QGraphicsOpacityEffect, QGraphicsDropShadowEffect
from PyQt6.QtGui import QColor, QPainter, QPen, QBrush, QFont, QPainterPath
import math
import random

class AnimationManager:
    """Central manager for all animations in the app"""
    
    def __init__(self):
        self.active_animations = []
        self.animation_speed = 1.0  # Global speed multiplier
    
    def create_fade_animation(self, widget, duration=300, start_opacity=0.0, end_opacity=1.0):
        """Create a smooth fade animation"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(int(duration / self.animation_speed))
        animation.setStartValue(start_opacity)
        animation.setEndValue(end_opacity)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.active_animations.append(animation)
        return animation
    
    def create_slide_animation(self, widget, duration=400, start_pos=None, end_pos=None, direction="left"):
        """Create a smooth slide animation"""
        if start_pos is None:
            if direction == "left":
                start_pos = QPoint(-widget.width(), widget.y())
            elif direction == "right":
                start_pos = QPoint(widget.parent().width(), widget.y())
            elif direction == "up":
                start_pos = QPoint(widget.x(), -widget.height())
            elif direction == "down":
                start_pos = QPoint(widget.x(), widget.parent().height())
        
        if end_pos is None:
            end_pos = widget.pos()
        
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(int(duration / self.animation_speed))
        animation.setStartValue(start_pos)
        animation.setEndValue(end_pos)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.active_animations.append(animation)
        return animation
    
    def create_scale_animation(self, widget, duration=300, start_scale=0.8, end_scale=1.0):
        """Create a smooth scale animation"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(int(duration / self.animation_speed))
        
        # Calculate scaled geometry
        original_rect = widget.geometry()
        center = original_rect.center()
        
        start_size = QSize(int(original_rect.width() * start_scale), 
                          int(original_rect.height() * start_scale))
        start_rect = QRect(0, 0, start_size.width(), start_size.height())
        start_rect.moveCenter(center)
        
        animation.setStartValue(start_rect)
        animation.setEndValue(original_rect)
        animation.setEasingCurve(QEasingCurve.Type.OutBack)
        
        self.active_animations.append(animation)
        return animation
    
    def create_bounce_animation(self, widget, duration=600):
        """Create a playful bounce animation"""
        original_pos = widget.pos()
        
        # Create sequence of bounces
        sequence = QSequentialAnimationGroup()
        
        # First bounce up
        bounce1 = QPropertyAnimation(widget, b"pos")
        bounce1.setDuration(int(duration * 0.3 / self.animation_speed))
        bounce1.setStartValue(original_pos)
        bounce1.setEndValue(QPoint(original_pos.x(), original_pos.y() - 20))
        bounce1.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # Fall down
        fall1 = QPropertyAnimation(widget, b"pos")
        fall1.setDuration(int(duration * 0.3 / self.animation_speed))
        fall1.setStartValue(QPoint(original_pos.x(), original_pos.y() - 20))
        fall1.setEndValue(QPoint(original_pos.x(), original_pos.y() + 5))
        fall1.setEasingCurve(QEasingCurve.Type.InCubic)
        
        # Small bounce back
        bounce2 = QPropertyAnimation(widget, b"pos")
        bounce2.setDuration(int(duration * 0.2 / self.animation_speed))
        bounce2.setStartValue(QPoint(original_pos.x(), original_pos.y() + 5))
        bounce2.setEndValue(QPoint(original_pos.x(), original_pos.y() - 5))
        bounce2.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # Settle
        settle = QPropertyAnimation(widget, b"pos")
        settle.setDuration(int(duration * 0.2 / self.animation_speed))
        settle.setStartValue(QPoint(original_pos.x(), original_pos.y() - 5))
        settle.setEndValue(original_pos)
        settle.setEasingCurve(QEasingCurve.Type.InCubic)
        
        sequence.addAnimation(bounce1)
        sequence.addAnimation(fall1)
        sequence.addAnimation(bounce2)
        sequence.addAnimation(settle)
        
        self.active_animations.append(sequence)
        return sequence
    
    def create_pulse_animation(self, widget, duration=1000, scale_factor=1.1):
        """Create a pulsing animation that loops"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(int(duration / self.animation_speed))
        
        original_rect = widget.geometry()
        center = original_rect.center()
        
        scaled_size = QSize(int(original_rect.width() * scale_factor), 
                           int(original_rect.height() * scale_factor))
        scaled_rect = QRect(0, 0, scaled_size.width(), scaled_size.height())
        scaled_rect.moveCenter(center)
        
        animation.setStartValue(original_rect)
        animation.setKeyValueAt(0.5, scaled_rect)
        animation.setEndValue(original_rect)
        animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        animation.setLoopCount(-1)  # Loop forever
        
        self.active_animations.append(animation)
        return animation
    
    def create_glow_effect(self, widget, color=QColor(74, 158, 255), blur_radius=20):
        """Create a glowing shadow effect"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setColor(color)
        shadow.setOffset(0, 0)
        widget.setGraphicsEffect(shadow)
        return shadow
    
    def animate_number_change(self, label, old_value, new_value, duration=800):
        """Animate number changes (like XP gains)"""
        def update_number():
            progress = animation.currentTime() / animation.duration()
            current_value = old_value + (new_value - old_value) * progress
            label.setText(str(int(current_value)))
        
        animation = QPropertyAnimation()
        animation.setDuration(int(duration / self.animation_speed))
        animation.valueChanged.connect(update_number)
        animation.setStartValue(0)
        animation.setEndValue(1)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.active_animations.append(animation)
        return animation

class AnimatedWidget(QWidget):
    """Base class for widgets with built-in animation support"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.animation_manager = AnimationManager()
        self.hover_animation = None
        self.click_animation = None
        self._hover_scale = 1.05
        self._click_scale = 0.95
        
    def enterEvent(self, event):
        """Animate on mouse enter"""
        if self.hover_animation:
            self.hover_animation.stop()
        
        self.hover_animation = self.animation_manager.create_scale_animation(
            self, duration=200, start_scale=1.0, end_scale=self._hover_scale
        )
        self.hover_animation.start()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animate on mouse leave"""
        if self.hover_animation:
            self.hover_animation.stop()
        
        self.hover_animation = self.animation_manager.create_scale_animation(
            self, duration=200, start_scale=self._hover_scale, end_scale=1.0
        )
        self.hover_animation.start()
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """Animate on mouse press"""
        if self.click_animation:
            self.click_animation.stop()
        
        self.click_animation = self.animation_manager.create_scale_animation(
            self, duration=100, start_scale=self._hover_scale, end_scale=self._click_scale
        )
        self.click_animation.start()
        super().mousePressEvent(event)
    
    def mouseReleaseEvent(self, event):
        """Animate on mouse release"""
        if self.click_animation:
            self.click_animation.stop()
        
        self.click_animation = self.animation_manager.create_scale_animation(
            self, duration=150, start_scale=self._click_scale, end_scale=self._hover_scale
        )
        self.click_animation.start()
        super().mouseReleaseEvent(event)

class ParticleSystem(QWidget):
    """Particle system for celebrations and effects"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.particles = []
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_particles)
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents)
        
    def emit_particles(self, position, count=20, particle_type="confetti"):
        """Emit particles from a position"""
        for _ in range(count):
            particle = {
                'x': position.x(),
                'y': position.y(),
                'vx': random.uniform(-5, 5),
                'vy': random.uniform(-8, -2),
                'life': 1.0,
                'decay': random.uniform(0.01, 0.03),
                'color': QColor(random.randint(100, 255), random.randint(100, 255), random.randint(100, 255)),
                'size': random.uniform(3, 8),
                'type': particle_type
            }
            self.particles.append(particle)
        
        if not self.timer.isActive():
            self.timer.start(16)  # ~60 FPS
    
    def update_particles(self):
        """Update particle physics"""
        for particle in self.particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['vy'] += 0.2  # Gravity
            particle['life'] -= particle['decay']
            
            if particle['life'] <= 0:
                self.particles.remove(particle)
        
        if not self.particles:
            self.timer.stop()
        
        self.update()
    
    def paintEvent(self, event):
        """Draw particles"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        for particle in self.particles:
            color = particle['color']
            color.setAlphaF(particle['life'])
            painter.setBrush(QBrush(color))
            painter.setPen(Qt.PenStyle.NoPen)
            
            size = particle['size'] * particle['life']
            painter.drawEllipse(int(particle['x'] - size/2), int(particle['y'] - size/2), 
                              int(size), int(size))

class AdvancedParticleSystem(QWidget):
    """Advanced particle system with multiple effects"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.particles = []
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_particles)
        self.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents)
        self.setGeometry(0, 0, parent.width() if parent else 800, parent.height() if parent else 600)

    def create_success_explosion(self, position, intensity=1.0):
        """Create a success explosion with stars and sparkles"""
        colors = [QColor(255, 215, 0), QColor(255, 165, 0), QColor(255, 255, 255), QColor(74, 158, 255)]

        # Main explosion
        for _ in range(int(30 * intensity)):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(3, 8) * intensity
            particle = {
                'x': position.x(),
                'y': position.y(),
                'vx': math.cos(angle) * speed,
                'vy': math.sin(angle) * speed - random.uniform(1, 3),
                'life': 1.0,
                'decay': random.uniform(0.008, 0.015),
                'color': random.choice(colors),
                'size': random.uniform(4, 10) * intensity,
                'type': 'star',
                'rotation': 0,
                'rotation_speed': random.uniform(-5, 5)
            }
            self.particles.append(particle)

        # Sparkles
        for _ in range(int(20 * intensity)):
            particle = {
                'x': position.x() + random.uniform(-50, 50),
                'y': position.y() + random.uniform(-50, 50),
                'vx': random.uniform(-2, 2),
                'vy': random.uniform(-4, -1),
                'life': 1.0,
                'decay': random.uniform(0.01, 0.02),
                'color': QColor(255, 255, 255),
                'size': random.uniform(2, 5),
                'type': 'sparkle',
                'twinkle': random.uniform(0, 2 * math.pi)
            }
            self.particles.append(particle)

        self.start_animation()

    def create_xp_gain_effect(self, position, xp_amount):
        """Create XP gain floating numbers and particles"""
        # Floating XP text
        xp_particle = {
            'x': position.x(),
            'y': position.y(),
            'vx': 0,
            'vy': -2,
            'life': 1.0,
            'decay': 0.005,
            'color': QColor(74, 158, 255),
            'size': 16,
            'type': 'xp_text',
            'text': f"+{xp_amount} XP"
        }
        self.particles.append(xp_particle)

        # XP orbs
        for _ in range(8):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(1, 3)
            particle = {
                'x': position.x(),
                'y': position.y(),
                'vx': math.cos(angle) * speed,
                'vy': math.sin(angle) * speed - 1,
                'life': 1.0,
                'decay': 0.01,
                'color': QColor(74, 158, 255, 180),
                'size': random.uniform(3, 6),
                'type': 'orb',
                'pulse': random.uniform(0, 2 * math.pi)
            }
            self.particles.append(particle)

        self.start_animation()

    def create_level_up_celebration(self, position):
        """Create epic level up celebration"""
        # Golden explosion
        for _ in range(50):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(5, 12)
            particle = {
                'x': position.x(),
                'y': position.y(),
                'vx': math.cos(angle) * speed,
                'vy': math.sin(angle) * speed - random.uniform(2, 5),
                'life': 1.0,
                'decay': random.uniform(0.005, 0.012),
                'color': QColor(255, 215, 0),
                'size': random.uniform(6, 14),
                'type': 'golden_star',
                'rotation': 0,
                'rotation_speed': random.uniform(-8, 8)
            }
            self.particles.append(particle)

        # Ring of light
        for i in range(24):
            angle = (i / 24) * 2 * math.pi
            speed = 8
            particle = {
                'x': position.x(),
                'y': position.y(),
                'vx': math.cos(angle) * speed,
                'vy': math.sin(angle) * speed,
                'life': 1.0,
                'decay': 0.008,
                'color': QColor(255, 255, 255, 200),
                'size': 8,
                'type': 'light_ray'
            }
            self.particles.append(particle)

        self.start_animation()

    def start_animation(self):
        """Start the particle animation timer"""
        if not self.timer.isActive():
            self.timer.start(16)  # ~60 FPS

    def update_particles(self):
        """Update all particles"""
        for particle in self.particles[:]:
            # Update position
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']

            # Apply gravity (except for certain types)
            if particle['type'] not in ['xp_text', 'light_ray']:
                particle['vy'] += 0.15

            # Update life
            particle['life'] -= particle['decay']

            # Update special properties
            if 'rotation_speed' in particle:
                particle['rotation'] += particle['rotation_speed']

            if 'pulse' in particle:
                particle['pulse'] += 0.2

            if 'twinkle' in particle:
                particle['twinkle'] += 0.3

            # Remove dead particles
            if particle['life'] <= 0:
                self.particles.remove(particle)

        # Stop timer if no particles
        if not self.particles:
            self.timer.stop()

        self.update()

    def paintEvent(self, event):
        """Draw all particles"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        for particle in self.particles:
            self.draw_particle(painter, particle)

    def draw_particle(self, painter, particle):
        """Draw a single particle based on its type"""
        x, y = int(particle['x']), int(particle['y'])
        size = particle['size'] * particle['life']
        color = particle['color']
        color.setAlphaF(particle['life'])

        painter.setBrush(QBrush(color))
        painter.setPen(Qt.PenStyle.NoPen)

        if particle['type'] == 'star' or particle['type'] == 'golden_star':
            # Draw star shape
            self.draw_star(painter, x, y, size, particle.get('rotation', 0))

        elif particle['type'] == 'sparkle':
            # Draw twinkling sparkle
            alpha = (math.sin(particle['twinkle']) + 1) / 2
            sparkle_color = QColor(color)
            sparkle_color.setAlphaF(alpha * particle['life'])
            painter.setBrush(QBrush(sparkle_color))
            painter.drawEllipse(int(x - size/2), int(y - size/2), int(size), int(size))

        elif particle['type'] == 'orb':
            # Draw pulsing orb
            pulse_size = size * (1 + 0.3 * math.sin(particle['pulse']))
            painter.drawEllipse(int(x - pulse_size/2), int(y - pulse_size/2),
                              int(pulse_size), int(pulse_size))

        elif particle['type'] == 'xp_text':
            # Draw floating XP text
            painter.setPen(color)
            font = QFont("Segoe UI", int(size), QFont.Weight.Bold)
            painter.setFont(font)
            painter.drawText(x - 30, y, particle['text'])

        elif particle['type'] == 'light_ray':
            # Draw light ray
            painter.drawEllipse(int(x - size/2), int(y - size/2), int(size), int(size))

        else:
            # Default circle
            painter.drawEllipse(int(x - size/2), int(y - size/2), int(size), int(size))

    def draw_star(self, painter, x, y, size, rotation=0):
        """Draw a star shape"""
        painter.save()
        painter.translate(x, y)
        painter.rotate(rotation)

        # Create star path
        path = QPainterPath()
        outer_radius = size / 2
        inner_radius = outer_radius * 0.4

        for i in range(10):
            angle = (i * math.pi) / 5
            if i % 2 == 0:
                radius = outer_radius
            else:
                radius = inner_radius

            px = radius * math.cos(angle - math.pi / 2)
            py = radius * math.sin(angle - math.pi / 2)

            if i == 0:
                path.moveTo(px, py)
            else:
                path.lineTo(px, py)

        path.closeSubpath()
        painter.drawPath(path)
        painter.restore()

# Global animation manager instance
animation_manager = AnimationManager()
