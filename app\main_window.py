# Main application window for the Math Re-teach App
from PyQt5.QtWidgets import Q<PERSON>ain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QStackedWidget, QPushButton, QHBoxLayout, QLabel, QAction, QMenuBar, QFrame, QSizePolicy
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont
from app.pages import LessonsPage, CheatSheetsPage
from app.progress.xp_system import load_progress

class SidebarButton(QPushButton):
    def __init__(self, text, icon=None):
        super().__init__(text)
        if icon:
            self.setIcon(QIcon(icon))
        self.setMinimumHeight(48)
        self.setFont(QFont('Segoe UI', 12))
        self.setStyleSheet('QPushButton { text-align: left; padding-left: 16px; border-radius: 8px; margin: 4px; } QPushButton:checked { background: #444; }')
        self.setCheckable(True)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("8th & 9th Grade Math Re-teach App")
        self.resize(1000, 700)
        self.is_dark_mode = False
        self.init_ui()

    def init_ui(self):
        # Menu bar for settings and mode switch
        menubar = QMenuBar(self)
        view_menu = menubar.addMenu("View")
        self.mode_action = QAction("Switch to Dark Mode", self)
        self.mode_action.triggered.connect(self.toggle_mode)
        view_menu.addAction(self.mode_action)
        self.setMenuBar(menubar)

        # Sidebar navigation
        sidebar = QFrame()
        sidebar.setStyleSheet('background: #232629;')
        sidebar.setFixedWidth(200)
        sidebar_layout = QVBoxLayout()
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        self.sidebar_buttons = {}
        nav_items = [
            ("Lessons", None),
            ("Cheat Sheets", None),
            ("Practice", None),
            ("Quizzes", None),
            ("Tests", None),
            ("Homework", None),
            ("Progress", None)
        ]
        for name, icon in nav_items:
            btn = SidebarButton(name, icon)
            btn.clicked.connect(lambda checked, n=name: self.navigate(n))
            sidebar_layout.addWidget(btn)
            self.sidebar_buttons[name] = btn
        sidebar_layout.addStretch()
        sidebar.setLayout(sidebar_layout)

        # Header with XP and progress
        header = QFrame()
        header.setStyleSheet('background: #1a1c1e;')
        header.setFixedHeight(60)
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(16, 0, 16, 0)
        self.xp_label = QLabel()
        self.xp_label.setFont(QFont('Segoe UI', 14, QFont.Bold))
        self.progress_bar = QLabel()
        self.progress_bar.setFont(QFont('Segoe UI', 12))
        header_layout.addWidget(self.xp_label)
        header_layout.addStretch()
        header_layout.addWidget(self.progress_bar)
        header.setLayout(header_layout)

        # Main content area
        self.stack = QStackedWidget()
        self.pages = {
            "Lessons": LessonsPage(),
            "Cheat Sheets": CheatSheetsPage(),
            "Practice": QLabel("Practice page coming soon!"),
            "Quizzes": QLabel("Quizzes page coming soon!"),
            "Tests": QLabel("Tests page coming soon!"),
            "Homework": QLabel("Homework page coming soon!"),
            "Progress": QLabel("Progress page coming soon!")
        }
        for page in self.pages.values():
            if isinstance(page, QLabel):
                page.setAlignment(Qt.AlignCenter)
            self.stack.addWidget(page)

        # Layout setup
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        main_layout.addWidget(header)
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        content_layout.addWidget(sidebar)
        content_layout.addWidget(self.stack)
        main_layout.addLayout(content_layout)
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        self.stack.setCurrentIndex(0)
        self.update_xp_progress()

    def navigate(self, name):
        idx = list(self.pages.keys()).index(name)
        self.stack.setCurrentIndex(idx)

    def toggle_mode(self):
        if self.is_dark_mode:
            self.setStyleSheet("")
            self.mode_action.setText("Switch to Dark Mode")
        else:
            self.setStyleSheet("""
                QMainWindow { background-color: #232629; color: #f0f0f0; }
                QPushButton { background-color: #333; color: #f0f0f0; border: none; padding: 8px; }
                QPushButton:hover { background-color: #444; }
                QLabel { color: #f0f0f0; }
                QMenuBar { background-color: #232629; color: #f0f0f0; }
            """)
            self.mode_action.setText("Switch to Light Mode")
        self.is_dark_mode = not self.is_dark_mode

    def update_xp_progress(self):
        data = load_progress()
        self.xp_label.setText(f"XP: {data['xp']} | Level: {data['level']}")
        total = 7  # lessons, update as needed
        done = len(data.get('lessons_completed', []))
        self.progress_bar.setText(f"Progress: {done}/{total} Lessons Completed")
