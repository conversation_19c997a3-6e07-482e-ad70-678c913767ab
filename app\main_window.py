# Main application window for the Math Re-teach App
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QStackedWidget,
                            QPushButton, QHBoxLayout, QLabel, QMenuBar,
                            QFrame, QSizePolicy, QProgressBar, QApplication)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPoint
from PyQt6.QtGui import QIcon, QFont, QPixmap, QPainter, QColor, QAction
from app.pages import LessonsPage, CheatSheetsPage, PracticePage, QuizPage, TestPage, HomeworkPage, ProgressPage
from app.progress.xp_system import load_progress, save_progress
from app.ui.themes import get_theme_stylesheet, get_available_themes, is_theme_unlocked
from app.ui.animations import animation_manager, AdvancedParticleSystem
from app.ui.premium_components import PremiumButton, GlassCard, AnimatedProgressBar, FloatingNotification, PulsingXPLabel
from app.settings import load_settings, save_settings

class ModernSidebarButton(QPushButton):
    def __init__(self, text, icon=None):
        super().__init__(text)
        if icon:
            self.setIcon(QIcon(icon))
        self.setMinimumHeight(52)
        self.setFont(QFont('Segoe UI', 14, QFont.Weight.Medium))
        self.setProperty("class", "sidebar-btn")
        self.setCheckable(True)
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.is_selected = False

        # Animation properties
        self._hover_scale = 1.02
        self._click_scale = 0.98
        self.hover_animation = None
        self.click_animation = None

    def setChecked(self, checked):
        super().setChecked(checked)
        self.is_selected = checked
        self.update()

    def enterEvent(self, event):
        """Animate on mouse enter"""
        if self.hover_animation:
            self.hover_animation.stop()

        self.hover_animation = animation_manager.create_scale_animation(
            self, duration=200, start_scale=1.0, end_scale=self._hover_scale
        )
        self.hover_animation.start()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Animate on mouse leave"""
        if self.hover_animation:
            self.hover_animation.stop()

        self.hover_animation = animation_manager.create_scale_animation(
            self, duration=200, start_scale=self._hover_scale, end_scale=1.0
        )
        self.hover_animation.start()
        super().leaveEvent(event)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 Math Re-teach App - Your Learning Journey")
        self.resize(1200, 800)
        self.setMinimumSize(900, 600)

        # Load settings and theme
        self.settings = load_settings()
        self.current_theme = self.settings.get('theme', 'dark_modern')

        # Initialize UI
        self.init_ui()
        self.apply_theme()

        # Setup particle system
        self.particle_system = AdvancedParticleSystem(self)
        self.particle_system.show()

        # Setup auto-save timer
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(30000)  # Auto-save every 30 seconds

        # Show welcome animation
        self.show_welcome_animation()

    def init_ui(self):
        # Create menu bar
        self.create_menu_bar()

        # Create main layout
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create header
        self.header = self.create_header()
        main_layout.addWidget(self.header)

        # Create content area with sidebar and main content
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Create sidebar
        self.sidebar = self.create_sidebar()
        content_layout.addWidget(self.sidebar)

        # Create main content stack
        self.stack = self.create_content_stack()
        content_layout.addWidget(self.stack)

        main_layout.addLayout(content_layout)
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        # Set initial page
        self.navigate("Lessons")
        self.update_xp_progress()

    def create_menu_bar(self):
        """Create the application menu bar"""
        menubar = QMenuBar(self)

        # View menu
        view_menu = menubar.addMenu("🎨 View")

        # Theme submenu
        theme_menu = view_menu.addMenu("Themes")
        for theme_name in get_available_themes():
            if is_theme_unlocked(theme_name):
                action = QAction(theme_name.replace('_', ' ').title(), self)
                action.triggered.connect(lambda checked, t=theme_name: self.change_theme(t))
                theme_menu.addAction(action)

        # Settings menu
        settings_menu = menubar.addMenu("⚙️ Settings")
        settings_action = QAction("Open Settings", self)
        settings_action.triggered.connect(self.open_settings)
        settings_menu.addAction(settings_action)

        self.setMenuBar(menubar)

    def create_sidebar(self):
        """Create the navigation sidebar"""
        sidebar = QFrame()
        sidebar.setObjectName("sidebar")
        sidebar.setFixedWidth(220)

        sidebar_layout = QVBoxLayout()
        sidebar_layout.setContentsMargins(12, 20, 12, 20)
        sidebar_layout.setSpacing(8)

        # App title in sidebar
        title_label = QLabel("📚 Math Learning")
        title_label.setFont(QFont('Segoe UI', 16, QFont.Weight.Bold))
        title_label.setProperty("class", "header")
        sidebar_layout.addWidget(title_label)

        sidebar_layout.addSpacing(20)

        # Navigation buttons
        self.sidebar_buttons = {}
        nav_items = [
            ("📖 Lessons", "Lessons"),
            ("📄 Cheat Sheets", "Cheat Sheets"),
            ("🧮 Practice", "Practice"),
            ("❓ Quizzes", "Quizzes"),
            ("🧪 Tests", "Tests"),
            ("📝 Homework", "Homework"),
            ("📊 Progress", "Progress")
        ]

        for display_name, key in nav_items:
            btn = ModernSidebarButton(display_name)
            btn.clicked.connect(lambda checked=False, n=key: self.navigate(n))
            sidebar_layout.addWidget(btn)
            self.sidebar_buttons[key] = btn

        sidebar_layout.addStretch()
        sidebar.setLayout(sidebar_layout)
        return sidebar

    def create_header(self):
        """Create the header with XP and progress info"""
        header = GlassCard()
        header.setObjectName("header")
        header.setFixedHeight(80)

        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(30, 10, 30, 10)

        # XP and Level info with pulsing animation
        self.xp_label = PulsingXPLabel()
        self.xp_label.setFont(QFont('Segoe UI', 18, QFont.Weight.Bold))
        self.xp_label.setProperty("class", "header")

        # Progress bar
        progress_container = QWidget()
        progress_layout = QVBoxLayout()
        progress_layout.setContentsMargins(0, 0, 0, 0)
        progress_layout.setSpacing(5)

        self.progress_label = QLabel()
        self.progress_label.setFont(QFont('Segoe UI', 12))
        self.progress_label.setProperty("class", "secondary")

        self.progress_bar = AnimatedProgressBar()
        self.progress_bar.setFixedHeight(8)

        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)
        progress_container.setLayout(progress_layout)

        # Add widgets to header
        header_layout.addWidget(self.xp_label)
        header_layout.addStretch()
        header_layout.addWidget(progress_container)

        header.setLayout(header_layout)
        return header

    def create_content_stack(self):
        """Create the main content stack with all pages"""
        stack = QStackedWidget()

        # Create all pages
        self.pages = {
            "Lessons": LessonsPage(),
            "Cheat Sheets": CheatSheetsPage(),
            "Practice": PracticePage(),
            "Quizzes": QuizPage(),
            "Tests": TestPage(),
            "Homework": HomeworkPage(),
            "Progress": ProgressPage()
        }

        # Add pages to stack
        for page in self.pages.values():
            stack.addWidget(page)

        return stack

    def navigate(self, name):
        """Navigate to a specific page with smooth transition"""
        if name in self.pages:
            # Get current and target pages
            current_idx = self.stack.currentIndex()
            target_idx = list(self.pages.keys()).index(name)

            if current_idx != target_idx:
                # Create slide transition
                current_page = self.stack.currentWidget()
                target_page = self.stack.widget(target_idx)

                # Fade out current page
                fade_out = animation_manager.create_fade_animation(
                    current_page, duration=200, start_opacity=1.0, end_opacity=0.0
                )

                # Switch page and fade in
                def switch_page():
                    self.stack.setCurrentIndex(target_idx)
                    fade_in = animation_manager.create_fade_animation(
                        target_page, duration=300, start_opacity=0.0, end_opacity=1.0
                    )
                    fade_in.start()

                fade_out.finished.connect(switch_page)
                fade_out.start()

            # Update sidebar button states with animation
            for btn_name, btn in self.sidebar_buttons.items():
                btn.setChecked(btn_name == name)
                if btn_name == name:
                    # Bounce animation for selected button
                    animation_manager.create_bounce_animation(btn, duration=400).start()

    def change_theme(self, theme_name):
        """Change the application theme"""
        self.current_theme = theme_name
        self.settings['theme'] = theme_name
        save_settings(self.settings)
        self.apply_theme()

    def apply_theme(self):
        """Apply the current theme to the application"""
        stylesheet = get_theme_stylesheet(self.current_theme)
        self.setStyleSheet(stylesheet)

    def open_settings(self):
        """Open the settings dialog"""
        from app.ui.settings_dialog import SettingsDialog
        dialog = SettingsDialog(self)
        dialog.settings_changed.connect(self.on_settings_changed)
        dialog.exec()

    def on_settings_changed(self):
        """Handle settings changes"""
        # Reload settings
        self.settings = load_settings()
        self.current_theme = self.settings.get('theme', 'dark_modern')

        # Apply new theme
        self.apply_theme()

        # Update any other UI elements that depend on settings
        self.update_xp_progress()

    def update_xp_progress(self, show_animation=False, xp_gained=0):
        """Update XP and progress display with animations"""
        data = load_progress()

        # Update XP and level with animation
        xp = data.get('xp', 0)
        level = data.get('level', 1)

        # Check for level up
        old_level = getattr(self, '_last_level', level)
        if level > old_level:
            self.show_level_up_celebration(level)
        self._last_level = level

        # Update XP label with pulsing animation
        if hasattr(self.xp_label, 'setXP'):
            self.xp_label.setXP(xp)
        else:
            self.xp_label.setText(f"🏆 Level {level} | ⭐ {xp} XP")

        # Show XP gain effect if specified
        if show_animation and xp_gained > 0:
            xp_pos = self.xp_label.mapTo(self, self.xp_label.rect().center())
            self.particle_system.create_xp_gain_effect(xp_pos, xp_gained)

            # Show floating notification
            notification = FloatingNotification(f"+{xp_gained} XP earned!", "success", self)
            notification.show_notification(2000)

        # Update progress with animation
        total_lessons = 10  # Update based on actual lesson count
        completed_lessons = len(data.get('lessons_completed', []))
        progress_percent = (completed_lessons / total_lessons) * 100 if total_lessons > 0 else 0

        self.progress_label.setText(f"📚 Progress: {completed_lessons}/{total_lessons} ({progress_percent:.0f}%)")
        self.progress_bar.setValue(progress_percent)

    def auto_save(self):
        """Auto-save user progress"""
        # This will be called every 30 seconds
        pass

    def show_welcome_animation(self):
        """Show welcome animation when app starts"""
        # Fade in the main window
        fade_in = animation_manager.create_fade_animation(
            self, duration=800, start_opacity=0.0, end_opacity=1.0
        )
        fade_in.start()

        # Slide in sidebar
        sidebar_slide = animation_manager.create_slide_animation(
            self.sidebar, duration=600, direction="left"
        )
        sidebar_slide.start()

        # Scale in header
        header_scale = animation_manager.create_scale_animation(
            self.header, duration=500, start_scale=0.8, end_scale=1.0
        )
        header_scale.start()

        # Welcome notification
        QTimer.singleShot(1000, lambda: self.show_welcome_notification())

    def show_welcome_notification(self):
        """Show welcome notification"""
        notification = FloatingNotification("🎉 Welcome to your Math Learning Journey!", "info", self)
        notification.show_notification(4000)

    def show_level_up_celebration(self, new_level):
        """Show epic level up celebration"""
        # Create level up particle effect
        center_pos = QPoint(self.width() // 2, self.height() // 2)
        self.particle_system.create_level_up_celebration(center_pos)

        # Show level up notification
        notification = FloatingNotification(f"🎉 LEVEL UP! You reached Level {new_level}!", "success", self)
        notification.show_notification(5000)

        # Play level up sound
        from app.audio.sound_manager import play_level_up
        play_level_up()

    def show_achievement_celebration(self, achievement_name):
        """Show achievement unlock celebration"""
        # Create achievement particle effect
        center_pos = QPoint(self.width() // 2, self.height() // 3)
        self.particle_system.create_success_explosion(center_pos, intensity=1.5)

        # Show achievement notification
        notification = FloatingNotification(f"🏆 Achievement Unlocked: {achievement_name}!", "warning", self)
        notification.show_notification(4000)

        # Play achievement sound
        from app.audio.sound_manager import play_achievement
        play_achievement()

    def closeEvent(self, event):
        """Handle application close event"""
        # Save settings before closing
        save_settings(self.settings)

        # Clean up particle system
        if hasattr(self, 'particle_system'):
            self.particle_system.timer.stop()

        event.accept()
