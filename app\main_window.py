# Main application window for the Math Re-teach App
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QStackedWidget,
                            QPushButton, QHBoxLayout, QLabel, QAction, QMenuBar,
                            QFrame, QSizePolicy, QProgressBar, QApplication)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QFont, QPixmap, QPainter, QColor
from app.pages import LessonsPage, CheatSheetsPage, PracticePage, QuizPage, TestPage, HomeworkPage, ProgressPage
from app.progress.xp_system import load_progress, save_progress
from app.ui.themes import get_theme_stylesheet, get_available_themes, is_theme_unlocked
from app.settings import load_settings, save_settings

class ModernSidebarButton(QPushButton):
    def __init__(self, text, icon=None):
        super().__init__(text)
        if icon:
            self.setIcon(QIcon(icon))
        self.setMinimumHeight(52)
        self.setFont(QFont('Segoe UI', 14, QFont.Weight.Medium))
        self.setProperty("class", "sidebar-btn")
        self.setCheckable(True)
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 Math Re-teach App - Your Learning Journey")
        self.resize(1200, 800)
        self.setMinimumSize(900, 600)

        # Load settings and theme
        self.settings = load_settings()
        self.current_theme = self.settings.get('theme', 'dark_modern')

        # Initialize UI
        self.init_ui()
        self.apply_theme()

        # Setup auto-save timer
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(30000)  # Auto-save every 30 seconds

    def init_ui(self):
        # Create menu bar
        self.create_menu_bar()

        # Create main layout
        main_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create header
        self.header = self.create_header()
        main_layout.addWidget(self.header)

        # Create content area with sidebar and main content
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Create sidebar
        self.sidebar = self.create_sidebar()
        content_layout.addWidget(self.sidebar)

        # Create main content stack
        self.stack = self.create_content_stack()
        content_layout.addWidget(self.stack)

        main_layout.addLayout(content_layout)
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

        # Set initial page
        self.navigate("Lessons")
        self.update_xp_progress()

    def create_menu_bar(self):
        """Create the application menu bar"""
        menubar = QMenuBar(self)

        # View menu
        view_menu = menubar.addMenu("🎨 View")

        # Theme submenu
        theme_menu = view_menu.addMenu("Themes")
        for theme_name in get_available_themes():
            if is_theme_unlocked(theme_name):
                action = QAction(theme_name.replace('_', ' ').title(), self)
                action.triggered.connect(lambda checked, t=theme_name: self.change_theme(t))
                theme_menu.addAction(action)

        # Settings menu
        settings_menu = menubar.addMenu("⚙️ Settings")
        settings_action = QAction("Open Settings", self)
        settings_action.triggered.connect(self.open_settings)
        settings_menu.addAction(settings_action)

        self.setMenuBar(menubar)

    def create_sidebar(self):
        """Create the navigation sidebar"""
        sidebar = QFrame()
        sidebar.setObjectName("sidebar")
        sidebar.setFixedWidth(220)

        sidebar_layout = QVBoxLayout()
        sidebar_layout.setContentsMargins(12, 20, 12, 20)
        sidebar_layout.setSpacing(8)

        # App title in sidebar
        title_label = QLabel("📚 Math Learning")
        title_label.setFont(QFont('Segoe UI', 16, QFont.Weight.Bold))
        title_label.setProperty("class", "header")
        sidebar_layout.addWidget(title_label)

        sidebar_layout.addSpacing(20)

        # Navigation buttons
        self.sidebar_buttons = {}
        nav_items = [
            ("📖 Lessons", "Lessons"),
            ("📄 Cheat Sheets", "Cheat Sheets"),
            ("🧮 Practice", "Practice"),
            ("❓ Quizzes", "Quizzes"),
            ("🧪 Tests", "Tests"),
            ("📝 Homework", "Homework"),
            ("📊 Progress", "Progress")
        ]

        for display_name, key in nav_items:
            btn = ModernSidebarButton(display_name)
            btn.clicked.connect(lambda checked=False, n=key: self.navigate(n))
            sidebar_layout.addWidget(btn)
            self.sidebar_buttons[key] = btn

        sidebar_layout.addStretch()
        sidebar.setLayout(sidebar_layout)
        return sidebar

    def create_header(self):
        """Create the header with XP and progress info"""
        header = QFrame()
        header.setObjectName("header")
        header.setFixedHeight(70)

        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(20, 0, 20, 0)

        # XP and Level info
        self.xp_label = QLabel()
        self.xp_label.setFont(QFont('Segoe UI', 16, QFont.Weight.Bold))
        self.xp_label.setProperty("class", "header")

        # Progress info
        self.progress_label = QLabel()
        self.progress_label.setFont(QFont('Segoe UI', 14))
        self.progress_label.setProperty("class", "secondary")

        # Add widgets to header
        header_layout.addWidget(self.xp_label)
        header_layout.addStretch()
        header_layout.addWidget(self.progress_label)

        header.setLayout(header_layout)
        return header

    def create_content_stack(self):
        """Create the main content stack with all pages"""
        stack = QStackedWidget()

        # Create all pages
        self.pages = {
            "Lessons": LessonsPage(),
            "Cheat Sheets": CheatSheetsPage(),
            "Practice": PracticePage(),
            "Quizzes": QuizPage(),
            "Tests": TestPage(),
            "Homework": HomeworkPage(),
            "Progress": ProgressPage()
        }

        # Add pages to stack
        for page in self.pages.values():
            stack.addWidget(page)

        return stack

    def navigate(self, name):
        """Navigate to a specific page"""
        if name in self.pages:
            idx = list(self.pages.keys()).index(name)
            self.stack.setCurrentIndex(idx)

            # Update sidebar button states
            for btn_name, btn in self.sidebar_buttons.items():
                btn.setChecked(btn_name == name)

    def change_theme(self, theme_name):
        """Change the application theme"""
        self.current_theme = theme_name
        self.settings['theme'] = theme_name
        save_settings(self.settings)
        self.apply_theme()

    def apply_theme(self):
        """Apply the current theme to the application"""
        stylesheet = get_theme_stylesheet(self.current_theme)
        self.setStyleSheet(stylesheet)

    def open_settings(self):
        """Open the settings dialog"""
        from app.ui.settings_dialog import SettingsDialog
        dialog = SettingsDialog(self)
        dialog.settings_changed.connect(self.on_settings_changed)
        dialog.exec()

    def on_settings_changed(self):
        """Handle settings changes"""
        # Reload settings
        self.settings = load_settings()
        self.current_theme = self.settings.get('theme', 'dark_modern')

        # Apply new theme
        self.apply_theme()

        # Update any other UI elements that depend on settings
        self.update_xp_progress()

    def update_xp_progress(self):
        """Update XP and progress display"""
        data = load_progress()

        # Update XP and level
        xp = data.get('xp', 0)
        level = data.get('level', 1)
        self.xp_label.setText(f"🏆 Level {level} | ⭐ {xp} XP")

        # Update progress
        total_lessons = 10  # Update based on actual lesson count
        completed_lessons = len(data.get('lessons_completed', []))
        progress_percent = (completed_lessons / total_lessons) * 100 if total_lessons > 0 else 0

        self.progress_label.setText(f"📚 Progress: {completed_lessons}/{total_lessons} ({progress_percent:.0f}%)")

    def auto_save(self):
        """Auto-save user progress"""
        # This will be called every 30 seconds
        pass

    def closeEvent(self, event):
        """Handle application close event"""
        # Save settings before closing
        save_settings(self.settings)
        event.accept()
