# Settings Dialog for Math Re-teach App
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QCheckBox, QComboBox, QSlider, 
                            QGroupBox, QSpinBox, QTabWidget, QWidget,
                            QFormLayout, QButtonGroup, QRadioButton)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from app.settings import load_settings, save_settings
from app.ui.themes import get_available_themes, is_theme_unlocked

class SettingsDialog(QDialog):
    settings_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚙️ Settings")
        self.setModal(True)
        self.resize(500, 600)
        self.settings = load_settings()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        
        # Create tab widget
        tabs = QTabWidget()
        
        # Appearance tab
        appearance_tab = self.create_appearance_tab()
        tabs.addTab(appearance_tab, "🎨 Appearance")
        
        # Learning tab
        learning_tab = self.create_learning_tab()
        tabs.addTab(learning_tab, "📚 Learning")
        
        # Audio tab
        audio_tab = self.create_audio_tab()
        tabs.addTab(audio_tab, "🔊 Audio")
        
        # Advanced tab
        advanced_tab = self.create_advanced_tab()
        tabs.addTab(advanced_tab, "⚙️ Advanced")
        
        # Buttons
        button_layout = QHBoxLayout()
        
        reset_btn = QPushButton("🔄 Reset to Defaults")
        reset_btn.clicked.connect(self.reset_settings)
        
        cancel_btn = QPushButton("❌ Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        save_btn = QPushButton("✅ Save")
        save_btn.setProperty("class", "primary")
        save_btn.clicked.connect(self.save_settings)
        
        button_layout.addWidget(reset_btn)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)
        
        layout.addWidget(tabs)
        layout.addLayout(button_layout)
        self.setLayout(layout)

    def create_appearance_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Theme selection
        theme_group = QGroupBox("Theme")
        theme_layout = QVBoxLayout()
        
        self.theme_combo = QComboBox()
        available_themes = []
        for theme_name in get_available_themes():
            if is_theme_unlocked(theme_name):
                display_name = theme_name.replace('_', ' ').title()
                self.theme_combo.addItem(display_name, theme_name)
                available_themes.append(theme_name)
        
        current_theme = self.settings.get('theme', 'dark_modern')
        if current_theme in available_themes:
            index = available_themes.index(current_theme)
            self.theme_combo.setCurrentIndex(index)
        
        theme_layout.addWidget(QLabel("Select Theme:"))
        theme_layout.addWidget(self.theme_combo)
        theme_group.setLayout(theme_layout)
        
        # Font size
        font_group = QGroupBox("Font Settings")
        font_layout = QFormLayout()
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(10, 24)
        self.font_size_spin.setValue(self.settings.get('font_size', 14))
        
        font_layout.addRow("Font Size:", self.font_size_spin)
        font_group.setLayout(font_layout)
        
        layout.addWidget(theme_group)
        layout.addWidget(font_group)
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_learning_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Learning modes
        mode_group = QGroupBox("Learning Mode")
        mode_layout = QVBoxLayout()
        
        self.mode_group = QButtonGroup()
        
        # Free Mode
        self.free_mode = QRadioButton("🌟 Free Mode")
        self.free_mode.setToolTip("No timers, just learn at your own pace")
        
        # XP Grind Mode
        self.xp_mode = QRadioButton("⚡ XP Grind Mode")
        self.xp_mode.setToolTip("Timed sessions with XP boosts and focus features")
        
        # Challenge Mode
        self.challenge_mode = QRadioButton("🔥 Challenge Mode")
        self.challenge_mode.setToolTip("No hints, test-only runs for advanced learners")
        
        self.mode_group.addButton(self.free_mode, 0)
        self.mode_group.addButton(self.xp_mode, 1)
        self.mode_group.addButton(self.challenge_mode, 2)
        
        # Set current mode
        if self.settings.get('xp_grind_mode', False):
            self.xp_mode.setChecked(True)
        elif self.settings.get('challenge_mode', False):
            self.challenge_mode.setChecked(True)
        else:
            self.free_mode.setChecked(True)
        
        mode_layout.addWidget(self.free_mode)
        mode_layout.addWidget(self.xp_mode)
        mode_layout.addWidget(self.challenge_mode)
        mode_group.setLayout(mode_layout)
        
        # Study settings
        study_group = QGroupBox("Study Settings")
        study_layout = QFormLayout()
        
        self.daily_goal_spin = QSpinBox()
        self.daily_goal_spin.setRange(1, 10)
        self.daily_goal_spin.setValue(self.settings.get('daily_goal', 3))
        
        self.focus_duration_spin = QSpinBox()
        self.focus_duration_spin.setRange(5, 60)
        self.focus_duration_spin.setValue(self.settings.get('focus_session_duration', 25))
        self.focus_duration_spin.setSuffix(" minutes")
        
        study_layout.addRow("Daily Lesson Goal:", self.daily_goal_spin)
        study_layout.addRow("Focus Session Duration:", self.focus_duration_spin)
        study_group.setLayout(study_layout)
        
        # Difficulty settings
        difficulty_group = QGroupBox("Difficulty")
        difficulty_layout = QVBoxLayout()
        
        self.auto_hints = QCheckBox("Show hints automatically after 30 seconds")
        self.auto_hints.setChecked(self.settings.get('auto_hints', False))
        
        self.instant_retry = QCheckBox("Allow instant retry on wrong answers")
        self.instant_retry.setChecked(self.settings.get('chill_mode', True))
        
        difficulty_layout.addWidget(self.auto_hints)
        difficulty_layout.addWidget(self.instant_retry)
        difficulty_group.setLayout(difficulty_layout)
        
        layout.addWidget(mode_group)
        layout.addWidget(study_group)
        layout.addWidget(difficulty_group)
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_audio_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        audio_group = QGroupBox("Audio Settings")
        audio_layout = QVBoxLayout()
        
        self.audio_fx_check = QCheckBox("Enable sound effects")
        self.audio_fx_check.setChecked(self.settings.get('audio_fx', True))
        
        self.notifications_check = QCheckBox("Enable notifications")
        self.notifications_check.setChecked(self.settings.get('notifications', True))
        
        audio_layout.addWidget(self.audio_fx_check)
        audio_layout.addWidget(self.notifications_check)
        audio_group.setLayout(audio_layout)
        
        layout.addWidget(audio_group)
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_advanced_tab(self):
        widget = QWidget()
        layout = QVBoxLayout()
        
        advanced_group = QGroupBox("Advanced Settings")
        advanced_layout = QVBoxLayout()
        
        self.auto_save_check = QCheckBox("Auto-save progress")
        self.auto_save_check.setChecked(self.settings.get('auto_save', True))
        
        self.timers_check = QCheckBox("Enable homework timers")
        self.timers_check.setChecked(self.settings.get('timers_enabled', False))
        
        advanced_layout.addWidget(self.auto_save_check)
        advanced_layout.addWidget(self.timers_check)
        advanced_group.setLayout(advanced_layout)
        
        layout.addWidget(advanced_group)
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def save_settings(self):
        # Update settings from UI
        self.settings['theme'] = self.theme_combo.currentData()
        self.settings['font_size'] = self.font_size_spin.value()
        self.settings['daily_goal'] = self.daily_goal_spin.value()
        self.settings['focus_session_duration'] = self.focus_duration_spin.value()
        self.settings['audio_fx'] = self.audio_fx_check.isChecked()
        self.settings['notifications'] = self.notifications_check.isChecked()
        self.settings['auto_save'] = self.auto_save_check.isChecked()
        self.settings['timers_enabled'] = self.timers_check.isChecked()
        self.settings['auto_hints'] = self.auto_hints.isChecked()
        self.settings['chill_mode'] = self.instant_retry.isChecked()
        
        # Learning modes
        checked_mode = self.mode_group.checkedId()
        self.settings['xp_grind_mode'] = (checked_mode == 1)
        self.settings['challenge_mode'] = (checked_mode == 2)
        
        # Save to file
        save_settings(self.settings)
        
        # Emit signal
        self.settings_changed.emit()
        
        self.accept()

    def reset_settings(self):
        from app.settings import reset_settings
        reset_settings()
        self.settings = load_settings()
        self.settings_changed.emit()
        self.accept()
