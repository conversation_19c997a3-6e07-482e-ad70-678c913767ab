// qline.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qline.h>
%End

class QLine
{
%TypeHeaderCode
#include <qline.h>
%End

%PickleCode
    sipRes = Py_BuildValue("iiii", sipCpp->x1(), sipCpp->y1(), sipCpp->x2(), sipCpp->y2());
%End

public:
    QLine();
    QLine(const QPoint &pt1_, const QPoint &pt2_);
    QLine(int x1pos, int y1pos, int x2pos, int y2pos);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QLine()");
        }
        else
        {
            sipRes = PyUnicode_FromFormat(
                    "PyQt6.QtCore.QLine(%i, %i, %i, %i)",
                    sipCpp->x1(), sipCpp->y1(), sipCpp->x2(), sipCpp->y2());
        }
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    int x1() const;
    int y1() const;
    int x2() const;
    int y2() const;
    QPoint p1() const;
    QPoint p2() const;
    int dx() const;
    int dy() const;
    void translate(const QPoint &point);
    void translate(int adx, int ady);
%If (- Qt_6_8_0)
    bool operator==(const QLine &d) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QLine &d) const;
%End
    QLine translated(const QPoint &p) const;
    QLine translated(int adx, int ady) const;
    void setP1(const QPoint &aP1);
    void setP2(const QPoint &aP2);
    void setPoints(const QPoint &aP1, const QPoint &aP2);
    void setLine(int aX1, int aY1, int aX2, int aY2);
    QPoint center() const;
%If (Qt_6_4_0 -)
    QLineF toLineF() const;
%End
};

QDataStream &operator<<(QDataStream &, const QLine &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QLine & /Constrained/) /ReleaseGIL/;

class QLineF
{
%TypeHeaderCode
#include <qline.h>
%End

%PickleCode
    sipRes = Py_BuildValue("dddd", sipCpp->x1(), sipCpp->y1(), sipCpp->x2(), sipCpp->y2());
%End

public:
    enum IntersectionType
    {
        NoIntersection,
        BoundedIntersection,
        UnboundedIntersection,
    };

    QLineF(const QLine &line);
    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    qreal length() const;
    QLineF unitVector() const;
    QLineF::IntersectionType intersects(const QLineF &l, QPointF *intersectionPoint /Out/ = 0) const;
    QLineF();
    QLineF(const QPointF &apt1, const QPointF &apt2);
    QLineF(qreal x1pos, qreal y1pos, qreal x2pos, qreal y2pos);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QLineF()");
        }
        else
        {
            PyObject *x1 = PyFloat_FromDouble(sipCpp->x1());
            PyObject *y1 = PyFloat_FromDouble(sipCpp->y1());
            PyObject *x2 = PyFloat_FromDouble(sipCpp->x2());
            PyObject *y2 = PyFloat_FromDouble(sipCpp->y2());
        
            if (x1 && y1 && x2 && y2)
            {
                sipRes = PyUnicode_FromFormat("PyQt6.QtCore.QLineF(%R, %R, %R, %R)",
                        x1, y1, x2, y2);
            }
        
            Py_XDECREF(x1);
            Py_XDECREF(y1);
            Py_XDECREF(x2);
            Py_XDECREF(y2);
        }
%End

    qreal x1() const;
    qreal y1() const;
    qreal x2() const;
    qreal y2() const;
    QPointF p1() const;
    QPointF p2() const;
    qreal dx() const;
    qreal dy() const;
    QLineF normalVector() const;
    void translate(const QPointF &point);
    void translate(qreal adx, qreal ady);
    void setLength(qreal len);
    QPointF pointAt(qreal t) const;
    QLine toLine() const;
%If (- Qt_6_8_0)
    bool operator==(const QLineF &d) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QLineF &d) const;
%End
    static QLineF fromPolar(qreal length, qreal angle);
    qreal angle() const;
    void setAngle(qreal angle);
    qreal angleTo(const QLineF &l) const;
    QLineF translated(const QPointF &p) const;
    QLineF translated(qreal adx, qreal ady) const;
    void setP1(const QPointF &aP1);
    void setP2(const QPointF &aP2);
    void setPoints(const QPointF &aP1, const QPointF &aP2);
    void setLine(qreal aX1, qreal aY1, qreal aX2, qreal aY2);
    QPointF center() const;
};

QDataStream &operator<<(QDataStream &, const QLineF &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QLineF & /Constrained/) /ReleaseGIL/;
%If (Qt_6_8_0 -)
bool operator!=(const QLineF &lhs, const QLine &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QLine &lhs, const QLineF &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QLineF &lhs, const QLineF &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QLine &lhs, const QLine &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QLineF &lhs, const QLine &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QLine &lhs, const QLineF &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QLineF &lhs, const QLineF &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QLine &lhs, const QLine &rhs);
%End
%If (Qt_6_8_0 -)
bool qFuzzyCompare(const QLineF &lhs, const QLineF &rhs);
%End
%If (Qt_6_8_0 -)
bool qFuzzyIsNull(const QLineF &line);
%End
