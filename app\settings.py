# Settings management for the Math Re-teach App
import json
import os

SETTINGS_FILE = os.path.join(os.path.dirname(__file__), 'settings.json')

DEFAULT_SETTINGS = {
    "theme": "dark_modern",
    "font_size": 14,
    "audio_fx": True,
    "timers_enabled": False,
    "chill_mode": True,  # disables cooldowns, allows instant retry
    "challenge_mode": False,  # disables hints, test-only
    "xp_grind_mode": False,  # enables focus sessions, XP boosts
    "auto_save": True,
    "notifications": True,
    "focus_session_duration": 25,  # minutes
    "daily_goal": 3,  # lessons per day
    "unlocked_themes": ["dark_modern", "light_modern"]
}

def load_settings():
    """Load settings from file or return defaults"""
    if os.path.exists(SETTINGS_FILE):
        try:
            with open(SETTINGS_FILE, 'r') as f:
                settings = json.load(f)
                # Merge with defaults to ensure all keys exist
                merged = DEFAULT_SETTINGS.copy()
                merged.update(settings)
                return merged
        except (json.JSONDecodeError, <PERSON><PERSON><PERSON><PERSON>):
            return DEFAULT_SETTINGS.copy()
    return DEFAULT_SETTINGS.copy()

def save_settings(settings):
    """Save settings to file"""
    try:
        with open(SETTINGS_FILE, 'w') as f:
            json.dump(settings, f, indent=2)
    except IOError:
        pass  # Fail silently if we can't save

def get_setting(key, default=None):
    """Get a specific setting value"""
    settings = load_settings()
    return settings.get(key, default)

def set_setting(key, value):
    """Set a specific setting value"""
    settings = load_settings()
    settings[key] = value
    save_settings(settings)

def reset_settings():
    """Reset all settings to defaults"""
    save_settings(DEFAULT_SETTINGS.copy())
