// qregularexpression.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRegularExpression
{
%TypeHeaderCode
#include <qregularexpression.h>
%End

public:
    enum PatternOption /BaseType=Flag/
    {
        NoPatternOption,
        CaseInsensitiveOption,
        DotMatchesEverythingOption,
        MultilineOption,
        ExtendedPatternSyntaxOption,
        InvertedGreedinessOption,
        DontCaptureOption,
        UseUnicodePropertiesOption,
    };

    typedef QFlags<QRegularExpression::PatternOption> PatternOptions;
    QRegularExpression::PatternOptions patternOptions() const;
    void setPatternOptions(QRegularExpression::PatternOptions options);
    QRegularExpression();
    QRegularExpression(const QString &pattern, QRegularExpression::PatternOptions options = QRegularExpression::NoPatternOption);
    QRegularExpression(const QRegularExpression &re);
    ~QRegularExpression();
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *uni = qpycore_PyObject_FromQString(sipCpp->pattern());
        
        if (uni)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtCore.QRegularExpression(%R", uni);
        
            if (sipCpp->patternOptions() != QRegularExpression::NoPatternOption)
            {
                qpycore_Unicode_ConcatAndDel(&sipRes,
                        PyUnicode_FromFormat(
                                ", PyQt6.QtCore.QRegularExpression.PatternOptions(%i)",
                                (int)sipCpp->patternOptions()));
            }
        
            qpycore_Unicode_ConcatAndDel(&sipRes, PyUnicode_FromString(")"));
        
            Py_DECREF(uni);
        }
        else
        {
            sipRes = 0;
        }
%End

    void swap(QRegularExpression &re /Constrained/);
    QString pattern() const;
    void setPattern(const QString &pattern);
    bool isValid() const;
    qsizetype patternErrorOffset() const;
    QString errorString() const;
    int captureCount() const;

    enum MatchType
    {
        NormalMatch,
        PartialPreferCompleteMatch,
        PartialPreferFirstMatch,
        NoMatch,
    };

    enum MatchOption /BaseType=Flag/
    {
        NoMatchOption,
        AnchorAtOffsetMatchOption,
        DontCheckSubjectStringMatchOption,
    };

    typedef QFlags<QRegularExpression::MatchOption> MatchOptions;
    QRegularExpressionMatch match(const QString &subject, qsizetype offset = 0, QRegularExpression::MatchType matchType = QRegularExpression::NormalMatch, QRegularExpression::MatchOptions matchOptions = QRegularExpression::NoMatchOption) const;
%If (Qt_6_5_0 -)
    QRegularExpressionMatch matchView(QStringView subjectView, qsizetype offset = 0, QRegularExpression::MatchType matchType = QRegularExpression::NormalMatch, QRegularExpression::MatchOptions matchOptions = QRegularExpression::NoMatchOption) const;
%End
    QRegularExpressionMatchIterator globalMatch(const QString &subject, qsizetype offset = 0, QRegularExpression::MatchType matchType = QRegularExpression::NormalMatch, QRegularExpression::MatchOptions matchOptions = QRegularExpression::NoMatchOption) const;
%If (Qt_6_5_0 -)
    QRegularExpressionMatchIterator globalMatchView(QStringView subjectView, qsizetype offset = 0, QRegularExpression::MatchType matchType = QRegularExpression::NormalMatch, QRegularExpression::MatchOptions matchOptions = QRegularExpression::NoMatchOption) const;
%End
    static QString escape(const QString &str);
    QStringList namedCaptureGroups() const;
%If (- Qt_6_8_0)
    bool operator==(const QRegularExpression &re) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QRegularExpression &re) const;
%End
    void optimize() const;
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    static QString wildcardToRegularExpression(QStringView str, QRegularExpression::WildcardConversionOptions options = QRegularExpression::DefaultWildcardConversion);
    static QString anchoredPattern(const QString &expression);

    enum WildcardConversionOption /BaseType=Flag/
    {
        DefaultWildcardConversion,
        UnanchoredWildcardConversion,
%If (Qt_6_6_0 -)
        NonPathWildcardConversion,
%End
    };

    typedef QFlags<QRegularExpression::WildcardConversionOption> WildcardConversionOptions;
    static QRegularExpression fromWildcard(QStringView pattern, Qt::CaseSensitivity cs = Qt::CaseInsensitive, QRegularExpression::WildcardConversionOptions options = QRegularExpression::DefaultWildcardConversion);
};

QDataStream &operator<<(QDataStream &out, const QRegularExpression &re /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &in, QRegularExpression &re /Constrained/) /ReleaseGIL/;

class QRegularExpressionMatch
{
%TypeHeaderCode
#include <qregularexpression.h>
%End

public:
    QRegularExpressionMatch();
    ~QRegularExpressionMatch();
    QRegularExpressionMatch(const QRegularExpressionMatch &match);
    void swap(QRegularExpressionMatch &match /Constrained/);
    QRegularExpression regularExpression() const;
    QRegularExpression::MatchType matchType() const;
    QRegularExpression::MatchOptions matchOptions() const;
    bool hasMatch() const;
    bool hasPartialMatch() const;
    bool isValid() const;
    int lastCapturedIndex() const;
    QString captured(int nth = 0) const;
%If (- Qt_6_8_0)
    QString captured(const QString &name) const;
%End
%If (Qt_6_8_0 -)
    QString captured(QAnyStringView name) const;
%End
    QStringList capturedTexts() const;
%If (- Qt_6_8_0)
    qsizetype capturedStart(QStringView name) const;
%End
%If (Qt_6_8_0 -)
    qsizetype capturedStart(QAnyStringView name) const;
%End
    qsizetype capturedStart(int nth = 0) const;
%If (- Qt_6_8_0)
    qsizetype capturedLength(QStringView name) const;
%End
%If (Qt_6_8_0 -)
    qsizetype capturedLength(QAnyStringView name) const;
%End
    qsizetype capturedLength(int nth = 0) const;
%If (- Qt_6_8_0)
    qsizetype capturedEnd(QStringView name) const;
%End
%If (Qt_6_8_0 -)
    qsizetype capturedEnd(QAnyStringView name) const;
%End
    qsizetype capturedEnd(int nth = 0) const;
%If (Qt_6_3_0 -)
    bool hasCaptured(int nth) const;
%End
%If (Qt_6_3_0 - Qt_6_8_0)
    bool hasCaptured(const QString &name) const;
%End
%If (Qt_6_8_0 -)
    bool hasCaptured(QAnyStringView name) const;
%End
};

class QRegularExpressionMatchIterator
{
%TypeHeaderCode
#include <qregularexpression.h>
%End

public:
    QRegularExpressionMatchIterator();
    ~QRegularExpressionMatchIterator();
    QRegularExpressionMatchIterator(const QRegularExpressionMatchIterator &iterator);
    void swap(QRegularExpressionMatchIterator &iterator /Constrained/);
    bool isValid() const;
    bool hasNext() const;
    QRegularExpressionMatch next();
    QRegularExpressionMatch peekNext() const;
    QRegularExpression regularExpression() const;
    QRegularExpression::MatchType matchType() const;
    QRegularExpression::MatchOptions matchOptions() const;
};

%If (Qt_6_8_0 -)
bool operator!=(const QRegularExpression &lhs, const QRegularExpression &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QRegularExpression &lhs, const QRegularExpression &rhs);
%End
